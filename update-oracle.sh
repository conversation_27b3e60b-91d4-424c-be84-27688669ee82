#!/bin/bash

GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
RESET='\033[0m' # Reset color

echo -e "${BLUE}Updating Oracle...${RESET}"

if [ -z "$1" ]; then
    echo -e "${YELLOW}No branch specified, checking out master...${RESET}"
    git checkout master
else
    echo -e "${YELLOW}Checking out branch $1...${RESET}"
    git checkout "$1" 2>/dev/null
    if [ $? -ne 0 ]; then
        # Branch does not exist, ask if it should be created
        echo -e "${RED}Branch $1 does not exist.${RESET}"
        read -p "Do you want to create the branch $1? [Y/n]: " create_branch
        create_branch=${create_branch:-Y} # Default to Y if no input is given
        if [[ "$create_branch" =~ ^[Yy]$ ]]; then
            echo -e "${YELLOW}Creating branch $1...${RESET}"
            git checkout -b "$1" || {
                echo -e "${RED}Failed to create branch $1${RESET}"
                exit 1
            }
        else
            echo -e "${RED}Exiting, branch not created.${RESET}"
            exit 1
        fi
    fi
fi

# Check if the branch has an upstream (remote) set
UPSTREAM=$(git rev-parse --abbrev-ref --symbolic-full-name @{u} 2>/dev/null)

if [ -z "$UPSTREAM" ]; then
    echo -e "${RED}No upstream branch set for $1. Skipping pull...${RESET}"
else
    echo -e "${YELLOW}Pulling the latest changes...${RESET}"
    git pull || {
        echo -e "${RED}Git pull failed! Exiting...${RESET}"
        exit 1
    }
fi

echo -e "${YELLOW}Installing dependencies with Yarn...${RESET}"
yarn || {
    echo -e "${RED}Yarn install failed! Exiting...${RESET}"
    exit 1
}

echo -e "${YELLOW}Syncing git hooks...${RESET}"
yarn lefthook install || {
    echo -e "${RED}Lefthook install failed! Exiting...${RESET}"
    exit 1
}

echo -e "${YELLOW}Running git checkout hook...${RESET}"
yarn lefthook run post-checkout || {
    echo -e "${RED}Post checkout hook failed! Exiting...${RESET}"
    exit 1
}

echo -e "${YELLOW}Updating .env...${RESET}"
node --import tsx scripts/update-env.ts || {
    echo -e "${RED}Failed updating .env! Exiting...${RESET}"
    exit 1
}

echo -e "${GREEN}Done updating Oracle!${RESET}"
