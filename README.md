#  Oracle

Oracle is een tool waarmee je snel een lokale omgeving kunt vergelijken met de productie variant. De tests worden gedraaid door middel van puppeteer, een javascript library wat meerdere browsers (headless) kan draaien.

Oracle bestaat uit twee onderdelen:

1) een NodeJS backend, die wordt aangesproken vanuit de terminal
2) een VueJS frontend, waarin de resultaten uit de backend bekeken kunnen worden.

## Tests uitvoeren

Voordat tests uitgevoerd kunnen worden moet brand informatie uit Artemis en Brand Websites opgehaald zijn. Dit kan worden gedaan met het volgende command:

`yarn update` of indien de alias geinstalleerd is: `oracle update`

Tests kun je uitvoeren door middel van de onderstaande commands. Bij het starten van de tests wordt er eerst een aantal inputs gevraagd, bijvoorbeeld welke pagina's en brands getest moeten worden. Voor veel onderdelen is een fallback beschikbaar.

`yarn; yarn test:frontend;`

### <PERSON>as

Je kunt het aanroepen van Oracle makkelijker maken door een Oracle alias toe te voegen aan je shell rc, bijvoorbeeld:

```bash
oracle() {
  cd /var/www/html/oracle || return
  yarn "${1:-install}"
  cd - || return
}
```

```nushell
def oracle [arg?] { cd /var/www/html/oracle; yarn ($arg | default 'install'); cd - }
```

Na het uitvoeren van `source ~/.zshrc` kun je dan Oracle gebruiken met `oracle test:frontend`.

## Tests inzien

Uitgevoerde tests kun je inzien, maar hiervoor is nu nog benodigd dat je de frontend server start. Dit kan met onderstaande commands worden afgetrapt.

`yarn; yarn serve;`

## Oracle config

Oracle bouwt bij het starten van de backend een config object op. Hierbij wordt ook gekeken naar een `oracle.config.json` in de `$HOME` directory.
Op basis van aanwezige waarden worden vragen bij het opstarten van de backend mogelijk verwijderd, aangezien er al waarden vastgezet zijn. Er wordt nu nog geen validatie uitgevoerd over de aanwezige configuratie of CLI argumenten.
Hieronder een voorbeeld van de `oracle.config.json` en welke waarden ondersteund worden.

```json
{
    "user": "ckaal",
    "randomBrandCount": 5,
    "brands": [
        "zapmeta"
    ],
    "viewports": [
        "desktop",
        "mobile"
    ],
    "browsers": [
        "chrome"
    ],
    "urls": [
        "/",
        "/dsr",
        "/dsrw"
    ]
}
```

## Unit tests

Unit tests kunnen uitgevoerd worden door middel van `yarn test`. Het gegenereerde coverage report kan [hier](file:///var/www/html/oracle/coverage/lcov-report/index.html) bekeken worden.

## Custom pipeline image

In samenwerking met OPS is een custom pipeline image opgezet om de snelheid van de pipelines drastisch te verbeteren. De reden dat de pipelines traag waren kwam door het downloaden van puppeteer met de benodigde dependencies en browsers. Deze zijn in de custom image al geinstalleerd en gebuild, waardoor het niet bij elke build moet plaatsvinden.
Mocht het nodig zijn om de Dockerfile bij te werken is deze terug te vinden in onderstaande link.

https://git.visymo.com/visymo/ci-cd/-/blob/master/templates/image_building/dockerfile_oracle
