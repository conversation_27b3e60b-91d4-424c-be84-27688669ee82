/** @type {import('ts-jest').JestConfigWithTsJest} **/
export default {
    preset: 'ts-jest',
    clearMocks: true,
    collectCoverage: true,
    collectCoverageFrom: [
        'src/backend/**/*.{ts,tsx}',
    ],
    coverageDirectory: "coverage",
    coveragePathIgnorePatterns: [
        '/types/',
    ],
    coverageReporters: [
        "json",
        "text",
        "lcov",
        "clover"
    ],
    coverageProvider: "v8",
    testEnvironment: "node",
    transform: {
        "^.+\\.m?[tj]sx?$": "babel-jest",
        "^.+\\.ts$": "ts-jest",
    },
    moduleNameMapper: {
        "^lodash-es$": "lodash",
        "^@/(.*)$": "<rootDir>/src/$1",
    },
    transformIgnorePatterns: [
        "node_modules/(?!(pixelmatch)/)"
    ],
    setupFiles: ['./jest.setup.js'],
};
