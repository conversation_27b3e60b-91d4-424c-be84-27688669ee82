import express from 'express';
import { createServer } from 'http';
import path from 'path';
import { fileURLToPath } from 'url';
import apiRoutes from './src/backend/routes/api.ts';
import WebSocketService from './src/backend/Services/WebSocketService.ts';
import Log from './src/backend/Common/Log.ts';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const server = createServer(app);
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// CORS middleware for development
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Request logging
app.use((req, res, next) => {
  Log.info(`${req.method} ${req.path} - ${req.ip}`);
  next();
});

// API routes
app.use('/api', apiRoutes);

// Serve static files from dist directory
app.use(express.static(path.join(__dirname, 'dist')));

// Serve screenshots directory
app.use('/screenshots', express.static(path.join(__dirname, 'public/screenshots')));

// Handle client-side routing - serve index.html for all non-API routes
app.get('*', (req, res) => {
  // Don't serve index.html for API routes or static assets
  if (req.path.startsWith('/api') || req.path.startsWith('/screenshots') || req.path.includes('.')) {
    res.status(404).json({ error: 'Not found' });
    return;
  }
  
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// Error handling middleware
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  Log.error(`Express error: ${err.message}`);
  res.status(500).json({
    success: false,
    error: process.env.NODE_ENV === 'production' ? 'Internal server error' : err.message,
  });
});

// Initialize WebSocket service
WebSocketService.initialize(server);

// Graceful shutdown
const gracefulShutdown = () => {
  Log.info('Received shutdown signal, closing server gracefully...');
  
  server.close(() => {
    Log.info('HTTP server closed');
    
    // Close WebSocket connections
    WebSocketService.close();
    
    // Exit process
    process.exit(0);
  });
  
  // Force close after 10 seconds
  setTimeout(() => {
    Log.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 10000);
};

// Handle shutdown signals
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  Log.error(`Uncaught Exception: ${error.message}`);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  Log.error(`Unhandled Rejection: ${reason}`);
  process.exit(1);
});

// Start server
server.listen(PORT, () => {
  Log.info(`Oracle server running on port ${PORT}`);
  Log.info(`Frontend: http://localhost:${PORT}`);
  Log.info(`API: http://localhost:${PORT}/api`);
  Log.info(`WebSocket: ws://localhost:${PORT}/ws`);
  Log.info(`Screenshots: http://localhost:${PORT}/screenshots`);
  
  if (process.env.NODE_ENV !== 'production') {
    Log.info('Development mode - CORS enabled for all origins');
  }
});

export default app;
