import {defineConfig} from 'vite';
import vue from '@vitejs/plugin-vue';
import {fileURLToPath} from "node:url";
import {createHtmlPlugin} from "vite-plugin-html";

export default defineConfig({
    plugins: [
        vue(),
        createHtmlPlugin({
            minify: true,
            entry: '../src/frontend/main.ts',
            template: 'public/index.html',
        }),
    ],
    build: {
        outDir: 'dist',
        sourcemap: true
    },
    server: {
        port: 8084,
        proxy: {
            '/api': {
                target: 'http://localhost:3000',
                changeOrigin: true,
                rewrite: (path) => path.replace(/^\/api/, ''),
            },
        },
    },
    resolve: {
        extensions: ['.ts', '.json', '.vue'],
        alias: {
            '@': fileURLToPath(new URL('./src', import.meta.url))
        },
    },
});
