import * as fs from "node:fs";
import * as path from "node:path";

const distFolder = path.join(process.cwd(), '/dist/screenshots');
const publicFolder = path.join(process.cwd(), '/public/screenshots');

if (fs.existsSync(distFolder)) {
    fs.rmSync(distFolder, { recursive: true, force: true });
}

fs.symlinkSync(publicFolder, distFolder, 'dir');

console.log(`Symlink created from ${publicFolder} to ${distFolder}`);
