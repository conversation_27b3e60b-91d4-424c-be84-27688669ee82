import fs from 'fs';
import path from 'path';

const envPath = path.resolve(process.cwd(), '.env');
const envDistPath = path.resolve(process.cwd(), '.env.dist');

function readEnvFile(filePath: string): Record<string, string> {
    if (!fs.existsSync(filePath)) {
        return {};
    }

    const content = fs.readFileSync(filePath, 'utf-8');
    const env: Record<string, string> = {};

    content.split('\n').forEach((line) => {
        if (line.trim() && !line.startsWith('#')) {
            const [key, value] = line.split('=').map((part) => part.trim());
            if (key) {
                env[key] = value ? value.replace(/"/g, '') : '';
            }
        }
    });

    return env;
}

function writeEnvFile(filePath: string, env: Record<string, string>) {
    const content = Object.entries(env)
        .map(([key, value]) => `${key}="${value}"`)
        .join('\n');

    fs.writeFileSync(filePath, content, 'utf-8');
}

function mergeEnvFiles() {
    const envDist = readEnvFile(envDistPath);
    const env = readEnvFile(envPath);

    const mergedEnv = {...envDist, ...env};

    writeEnvFile(envPath, mergedEnv);
    console.log(".env has been successfully merged with .env.dist");
}

if (fs.existsSync(envPath)) {
    mergeEnvFiles();
} else {
    const envDist = readEnvFile(envDistPath);

    writeEnvFile(envPath, envDist);
    console.log(".env has been created");
}
