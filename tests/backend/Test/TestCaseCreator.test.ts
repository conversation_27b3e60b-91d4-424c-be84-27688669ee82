import TestCaseCreator from '@/backend/Test/TestCaseCreator.ts';
import ClusterManager from '@/backend/Test/ClusterManager.ts';
import OracleConfig from '@/shared/types/Common/OracleConfig.ts';
import Brand from '@/backend/Common/Brand.ts';
import {describe, expect, it, jest, beforeEach} from '@jest/globals';
import {PuppeteerDriver} from '@/backend/Test/Driver/PuppeteerDriver.ts';

// Mock dependencies
jest.mock('@/backend/Test/FileWriter.ts');
jest.mock('@/backend/Lib/Application.ts', () => ({
    default: {
        screenshotPath: '/test/screenshots'
    }
}));
jest.mock('@/shared/config/overrides/brands.ts', () => ({
    default: {
        'test-brand': {
            additionalDomains: ['search']
        }
    }
}));

describe('TestCaseCreator', () => {
    let mockOracleConfig: OracleConfig;
    let mockClusterManager: ClusterManager;
    let testCaseCreator: TestCaseCreator;

    beforeEach(() => {
        // Create mock brand with all required properties
        const mockBrand = new Brand({
            slug: 'test-brand',
            domains: [
                { host: 'www.test.com', locale: 'en' },
                { host: 'nl.test.com', locale: 'nl' }
            ],
            google_adsense: {
                contract_type: 'online'
            }
        });

        // Create mock oracle config
        mockOracleConfig = {
            brands: [mockBrand],
            urls: ['/'],
            browsers: [{
                product: 'chrome',
                executable: '/usr/bin/chrome'
            }],
            viewports: [{
                name: 'desktop',
                width: 1920,
                height: 1080
            }],
            drivers: ['puppeteer'],
            user: 'test-user'
        } as OracleConfig;

        // Create mock cluster manager
        mockClusterManager = new ClusterManager(['local', 'production']);

        testCaseCreator = new TestCaseCreator(mockOracleConfig, mockClusterManager);
    });

    describe('constructor', () => {
        it('should create TestCaseCreator with valid config and cluster manager', () => {
            expect(testCaseCreator).toBeInstanceOf(TestCaseCreator);
        });

        it('should throw error when config is null', () => {
            expect(() => {
                new TestCaseCreator(null as any, mockClusterManager);
            }).toThrow('OracleConfig is required');
        });

        it('should throw error when cluster manager is null', () => {
            expect(() => {
                new TestCaseCreator(mockOracleConfig, null as any);
            }).toThrow('ClusterManager is required');
        });
    });

    describe('createTestItems', () => {
        it('should create test items with valid date', () => {
            const date = '2025-01-21';
            const testItems = testCaseCreator.createTestItems(date);

            expect(testItems).toBeDefined();
            expect(testItems['test-brand']).toBeDefined();
            expect(Array.isArray(testItems['test-brand'])).toBe(true);
        });

        it('should throw error with invalid date', () => {
            expect(() => {
                testCaseCreator.createTestItems('');
            }).toThrow('Valid date string is required');

            expect(() => {
                testCaseCreator.createTestItems(null as any);
            }).toThrow('Valid date string is required');
        });
    });

    describe('getTestCases', () => {
        it('should return empty array initially', () => {
            const testCases = testCaseCreator.getTestCases();
            expect(Array.isArray(testCases)).toBe(true);
            expect(testCases.length).toBe(0);
        });

        it('should return immutable copy of test cases', () => {
            const testCases1 = testCaseCreator.getTestCases();
            const testCases2 = testCaseCreator.getTestCases();
            
            expect(testCases1).not.toBe(testCases2); // Different instances
            expect(testCases1).toEqual(testCases2); // Same content
        });
    });

    describe('createTestCases', () => {
        beforeEach(() => {
            // Mock cluster manager methods
            jest.spyOn(mockClusterManager, 'initClusterForDriver').mockResolvedValue({
                queue: jest.fn()
            });
        });

        it('should create test cases with valid date', async () => {
            const date = '2025-01-21';
            
            await expect(testCaseCreator.createTestCases(date)).resolves.not.toThrow();
            
            const testCases = testCaseCreator.getTestCases();
            expect(testCases.length).toBeGreaterThan(0);
        });

        it('should throw error with invalid date', async () => {
            await expect(testCaseCreator.createTestCases('')).rejects.toThrow('Valid date string is required');
            await expect(testCaseCreator.createTestCases(null as any)).rejects.toThrow('Valid date string is required');
        });
    });
});
