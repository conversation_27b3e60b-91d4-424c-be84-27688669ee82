import ClusterManager from '@/backend/Test/ClusterManager.ts';
import Browser from '@/shared/types/Config/Browser.ts';

describe('ClusterManager', () => {
    let clusterManager: ClusterManager;
    let mockDriver: any;
    let mockBrowser: Browser;

    beforeEach(() => {
        clusterManager = new ClusterManager(['test']);
        mockDriver = {name: 'chrome', initCluster: jest.fn().mockResolvedValue({idle: jest.fn(), close: jest.fn()})};
        mockBrowser = {product: 'chrome'} as Browser;
    });

    test('should initialize and track clusters', async () => {
        const cluster = await clusterManager.initClusterForDriver(mockDriver, mockBrowser);
        expect(cluster).toBeDefined();
        expect(clusterManager.getClusters()).toHaveLength(1);
    });

    test('should reuse clusters for the same driver and browser', async () => {
        const firstCluster = await clusterManager.initClusterForDriver(mockDriver, mockBrowser);
        const secondCluster = await clusterManager.initClusterForDriver(mockDriver, mockBrowser);

        expect(firstCluster).toBe(secondCluster);
        expect(clusterManager.getClusters()).toHaveLength(1);
    });

    test('should create a new cluster for different driver', async () => {
        const mockDriver2 = {
            name: 'firefox',
            initCluster: jest.fn().mockResolvedValue({idle: jest.fn(), close: jest.fn()}),
        };
        const firstCluster = await clusterManager.initClusterForDriver(mockDriver, mockBrowser);
        const secondCluster = await clusterManager.initClusterForDriver(mockDriver2, mockBrowser);

        expect(firstCluster).not.toBe(secondCluster);
        expect(clusterManager.getClusters()).toHaveLength(2);
    });
});
