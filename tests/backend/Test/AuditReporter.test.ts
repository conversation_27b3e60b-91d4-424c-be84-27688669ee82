import AuditReporter from '@/backend/Test/AuditReporter.ts';
import ScreenshotCompare from '@/backend/Test/ScreenshotCompare.ts';
import fileWriter from '@/backend/Test/FileWriter.ts';
import ProgressBar from '@/backend/Test/ProgressBar.ts';
import {beforeEach, describe, expect, it, jest} from '@jest/globals';
import {TestCaseStubs} from '../../stubs/TestCaseStubs.ts';
import ScreenshotTestResult from '@/backend/Test/ScreenshotTestResult.ts';
import {SingleBar} from 'cli-progress';

jest.mock('@/backend/Common/Log');
jest.mock('@/backend/Test/ScreenshotCompare');
jest.mock('@/backend/Test/FileWriter');
jest.mock('cli-progress');
jest.mock('@/backend/Test/ProgressBar');

const mockFileWriter: jest.Mocked<typeof fileWriter> = <jest.Mocked<typeof fileWriter>>fileWriter;
const mockProgressBar: jest.Mocked<typeof ProgressBar> = <jest.Mocked<typeof ProgressBar>>ProgressBar;

describe('AuditReporter', () => {
    const date = '2025-02-25';
    const testCaseStubs = TestCaseStubs.get();

    beforeEach(() => {
        jest.clearAllMocks();

        mockProgressBar.create.mockImplementation(() => ({
            start: jest.fn(),
            stop: jest.fn(),
            increment: jest.fn(),
        }) as unknown as SingleBar);
    });

    it('should report the audit correctly', async () => {
        (ScreenshotCompare.testCase as jest.Mock).mockImplementation(() => new ScreenshotTestResult(
            testCaseStubs[0],
            100,
            false,
        ));
        (ScreenshotCompare.testCase as jest.Mock).mockImplementation(() => new ScreenshotTestResult(
            testCaseStubs[1],
            100,
            false,
        ));

        mockFileWriter.writeSafe.mockImplementation(() => {
        });

        await AuditReporter.report(testCaseStubs, date);

        expect(mockFileWriter.writeSafe).toHaveBeenCalledWith(
            expect.stringContaining(date),
            expect.any(String),
            false,
        );
    });

    it('should perform the audit correctly', async () => {
        const screenshotTestResult1 = new ScreenshotTestResult(
            testCaseStubs[0],
            100,
            false,
        );
        const screenshotTestResult2 = new ScreenshotTestResult(
            testCaseStubs[1],
            0,
            true,
        );
        (ScreenshotCompare.testCase as jest.Mock)
            .mockImplementationOnce(() => screenshotTestResult1)
            .mockImplementationOnce(() => screenshotTestResult2);

        const diffProgress = mockProgressBar.create();
        diffProgress.start(testCaseStubs.length, 0);
        const result = await AuditReporter.performAudit(testCaseStubs, diffProgress, date);

        expect(result).toEqual([screenshotTestResult1]);
        expect(diffProgress.increment).toHaveBeenCalledTimes(testCaseStubs.length);

        expect(mockFileWriter.writeSafe).toHaveBeenCalledWith(
            expect.stringContaining(date),
            expect.any(String),
            false,
        );
    });
});
