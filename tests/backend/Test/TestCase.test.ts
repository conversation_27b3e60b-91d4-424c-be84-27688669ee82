import TestCaseFactory from '@/backend/Test/TestCaseFactory.ts';
import * as path from 'node:path';
import UrlSetFactory from '@/backend/Test/UrlSetFactory.ts';
import Browser from '@/shared/types/Config/Browser.ts';
import {describe, expect, it, jest} from '@jest/globals';
import browsers from '@/shared/config/browsers.ts';
import {find} from 'lodash-es';
import {PuppeteerDriver} from '@/backend/Test/Driver/PuppeteerDriver.ts';
import Viewport from '@/shared/types/Config/Viewport.ts';
import viewports from '@/shared/config/viewports.ts';

jest.mock('os', () => ({
    userInfo: jest.fn().mockReturnValue({username: 'user.name'}),
}));

const chromeBrowser = find(browsers, (browser: Browser) => browser.product === 'chrome');
const desktopViewport = find(viewports, (viewport: Viewport) => viewport.name === 'desktop');

describe('TestCaseFactory', () => {
    it('should create a valid TestCase with the correct file path and parameters', () => {
        const date = '2025-01-21';
        const brandSlug = 'zapmeta';
        const urlSet = UrlSetFactory.create(
            'www.zapmeta.com',
            '/dsr',
        );
        const user = 'testUser';
        const driver = new PuppeteerDriver();

        const testCase = TestCaseFactory.create(
            date,
            brandSlug,
            urlSet,
            chromeBrowser,
            desktopViewport,
            driver,
            user,
        );

        const expectedFilePath = path.normalize(
            `/public/screenshots/2025-01-21/zapmeta/puppeteer/chrome/dsr/desktop/www.zapmeta.com`,
        );
        expect(testCase.filePath).toContain(expectedFilePath);

        expect(testCase.urlSet).toBe(urlSet);
        expect(testCase.brandSlug).toBe(brandSlug);
        expect(testCase.browser).toBe(chromeBrowser);
        expect(testCase.viewport).toEqual(desktopViewport);
        expect(testCase.user).toBe(user);
    });

    it('should default to the current system username if no user is provided', () => {
        const date = '2025-01-21';
        const brandSlug = 'zapmeta';
        const urlSet = UrlSetFactory.create(
            'www.zapmeta.com',
            '/msr',
        );
        const user = 'testUser';
        const driver = new PuppeteerDriver();

        const testCase = TestCaseFactory.create(
            date,
            brandSlug,
            urlSet,
            chromeBrowser,
            desktopViewport,
            driver,
            user,
        );

        expect(testCase.user).toBe('testUser');
    });

    it('should handle empty URLs by defaulting to "home"', () => {
        const date = '2025-01-21';
        const brandSlug = 'zapmeta';
        const urlSet = UrlSetFactory.create(
            'nl.zapmeta.com',
            '/',
        );
        const user = 'testUser';
        const driver = new PuppeteerDriver();

        const testCase = TestCaseFactory.create(
            date,
            brandSlug,
            urlSet,
            chromeBrowser,
            desktopViewport,
            driver,
            user,
        );

        const expectedFilePath = path.normalize(
            `/public/screenshots/2025-01-21/zapmeta/puppeteer/chrome/Home/desktop/nl.zapmeta.com`,
        );
        expect(testCase.filePath).toContain(expectedFilePath);
    });
});
