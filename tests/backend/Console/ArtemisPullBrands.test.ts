import ArtemisPullBrands from '@/backend/Console/ArtemisPullBrands.ts';
import fileWriter from '@/backend/Test/FileWriter.ts';
import Log from '@/backend/Common/Log.ts';
import {beforeEach, describe, expect, it, jest} from '@jest/globals';
import ArtemisClient from '@/backend/Lib/ArtemisClient.ts';
import Application from '@/backend/Lib/Application.ts';

jest.mock('@/backend/Lib/ArtemisClient');
jest.mock('@/backend/Test/FileWriter');
jest.mock('@/backend/Common/Log');

const mockFileWriter: jest.Mocked<typeof fileWriter> = <jest.Mocked<typeof fileWriter>>fileWriter;
const mockArtemisClient: jest.Mocked<typeof ArtemisClient> = <jest.Mocked<typeof ArtemisClient>>ArtemisClient;

describe('ArtemisPullBrands', () => {
    const filePath = Application.brandsJsonPath;

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should fetch brands and save to a file', async () => {
        const mockBrands = [{name: 'Brand1'}, {name: 'Brand2'}];
        mockArtemisClient.get.mockResolvedValue(mockBrands);

        mockFileWriter.writeSafe.mockImplementation(() => {
        });

        await ArtemisPullBrands(filePath);

        expect(ArtemisClient.get).toHaveBeenCalledWith('/api/brands');
        expect(mockFileWriter.writeSafe).toHaveBeenCalledWith(filePath, JSON.stringify(mockBrands, null, 2), false);
        expect(Log.info).toHaveBeenCalledWith('Pulled brands from Artemis and saved to file.');
    });

    it('should handle errors when the API call fails', async () => {
        mockArtemisClient.get.mockRejectedValue(new Error('API request failed'));

        await ArtemisPullBrands(filePath);

        expect(Log.error).toHaveBeenCalledWith('Failed to fetch or save brands: API request failed');
        expect(mockFileWriter.writeSafe).not.toHaveBeenCalled();
    });

    it('should handle errors when file writing fails', async () => {
        const mockBrands = [{name: 'Brand1'}];
        mockArtemisClient.get.mockResolvedValue(mockBrands);

        mockFileWriter.writeSafe.mockImplementation(() => {
            throw new Error('Failed to write file');
        });

        await ArtemisPullBrands(filePath);

        expect(Log.error).toHaveBeenCalledWith('Failed to fetch or save brands: Failed to write file');
    });
});