import * as fs from 'node:fs';
import * as path from 'node:path';
import Log from '@/backend/Common/Log.ts';
import {beforeEach, describe, expect, it, jest} from '@jest/globals';

jest.mock('node:fs');

const mockFs: jest.Mocked<typeof fs> = <jest.Mocked<typeof fs>>fs;

describe('Log Class', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should create the logs directory if it does not exist', () => {
        const logDir = path.resolve('public', 'logs');

        mockFs.existsSync.mockReturnValue(false);
        mockFs.readFileSync.mockReturnValue('{}');

        Log.init();

        expect(fs.mkdirSync).toHaveBeenCalledWith(logDir, {recursive: true});
    });

    it('should create a log file and append messages', () => {
        const message = 'Test message';
        const logDir = path.resolve('public', 'logs');
        const logFilePath = path.join(logDir, `log-${new Date().toISOString().split('T')[0]}.log`);

        Log.info(message);

        expect(fs.appendFileSync).toHaveBeenCalledWith(logFilePath, expect.stringContaining(message));
    });

    it('should format log messages with a timestamp and level', () => {
        const message = 'Test message';
        const level = 'INFO';
        const logDir = path.resolve('public', 'logs');
        const logFilePath = path.join(logDir, `log-${new Date().toISOString().split('T')[0]}.log`);
        const logMessage = `[${level}] ${message}`;

        mockFs.appendFileSync.mockImplementation(() => {
        });

        Log.log(message, level);

        expect(fs.appendFileSync).toHaveBeenCalledWith(logFilePath, expect.stringContaining(logMessage));
    });
});

