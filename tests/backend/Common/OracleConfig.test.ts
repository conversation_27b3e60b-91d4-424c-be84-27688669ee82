import OracleConfig from '@/backend/Common/OracleConfig.ts';
import * as fs from 'node:fs';
import BrandWebsitesBrandsReader from '@/backend/Test/BrandWebsitesBrandsReader.ts';
import viewports from '@/shared/config/viewports.ts';
import {beforeEach, describe, expect, it, jest} from '@jest/globals';
import Brand from '@/backend/Common/Brand.ts';
import {find} from 'lodash-es';
import Viewport from '@/shared/types/Config/Viewport.ts';

jest.mock('node:fs');
jest.mock('@/backend/Test/BrandWebsitesBrandsReader');

const desktopViewport = find(viewports, (viewport: Viewport) => viewport.name === 'desktop');

describe('OracleConfig', () => {
    let oracleConfig;

    beforeEach(() => {
        oracleConfig = new OracleConfig();
    });

    describe('readConfig', () => {
        it('should read config when file exists', async () => {
            (fs.existsSync as jest.Mock).mockReturnValue(true);
            (fs.readFileSync as jest.Mock).mockReturnValue('{}');

            const result = await oracleConfig.readConfig();

            expect(result.loaded).toBe(true);
            expect(fs.existsSync).toHaveBeenCalledWith(`${process.env.HOME}/oracle.config.json`);
        });

        it('should not read config when file does not exist', async () => {
            (fs.existsSync as jest.Mock).mockReturnValue(false);

            const result = await oracleConfig.readConfig();

            expect(result.loaded).toBe(false);
            expect(fs.existsSync).toHaveBeenCalledWith(`${process.env.HOME}/oracle.config.json`);
        });
    });

    describe('mergeConfig', () => {
        it('should merge additional config correctly', async () => {
            const additionalConfig = {viewports: ['tablet']};
            oracleConfig.config = {viewports: ['desktop'], browsers: ['chrome']};

            oracleConfig.mergeConfig(additionalConfig);

            expect(oracleConfig.config.viewports).toEqual(['tablet', 'desktop']);
            expect(oracleConfig.config.browsers).toEqual(['chrome']);
        });

        it('should retain existing config when no additional config is passed', async () => {
            oracleConfig.mergeConfig({viewports: ['desktop'], browsers: ['chrome']});

            expect(oracleConfig.config.viewports).toEqual(['desktop']);
            expect(oracleConfig.config.browsers).toEqual(['chrome']);
        });
    });

    describe('setDefaults', () => {
        it('should handle the case when arguments are passed for parameters', async () => {
            process.argv = ['node', '--import', 'tsx', 'src/backend/Console/FrontendTest.ts', '--brands', 'zapmeta,izito', '--viewports', 'desktop'];

            (BrandWebsitesBrandsReader.getBySlug as jest.Mock).mockImplementation((slug) => new Brand({slug}));
            (BrandWebsitesBrandsReader.getRandomBrands as jest.Mock).mockImplementation(() => [
                new Brand({slug: 'zapmeta'}),
                new Brand({slug: 'izito'}),
            ]);

            oracleConfig = await oracleConfig.readConfig();
            oracleConfig = oracleConfig.setDefaults();

            expect(oracleConfig.config.brands).toEqual([
                new Brand({slug: 'zapmeta'}),
                new Brand({slug: 'izito'}),
            ]);
            expect(oracleConfig.config.viewports).toEqual([desktopViewport]);
        });

        it('should handle empty parameters gracefully', async () => {
            process.argv = ['node', '--import', 'tsx', 'src/backend/Console/FrontendTest.ts'];

            (BrandWebsitesBrandsReader.getRandomBrands as jest.Mock).mockReturnValue(['zapmeta', 'breanto']);

            oracleConfig = await oracleConfig.readConfig();
            oracleConfig = oracleConfig.setDefaults();

            expect(oracleConfig.config.brands).toEqual([
                new Brand({slug: 'zapmeta'}),
                new Brand({slug: 'breanto'}),
            ]);
        });
    });
});
