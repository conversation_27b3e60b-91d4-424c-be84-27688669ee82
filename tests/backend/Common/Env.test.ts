import * as dotenv from 'dotenv';
import Env from '@/backend/Common/Env.ts';
import {beforeEach, describe, expect, it, jest} from '@jest/globals';

jest.mock('dotenv');

describe('Env class', () => {
    beforeEach(() => {
        process.env = {};
    });

    it('should load dotenv config on initialization', () => {
        Env.init();

        expect(dotenv.config).toHaveBeenCalledWith({
            path: ['.env', '.env.dist'],
        });
    });

    it('should return the correct environment variable when set', () => {
        process.env.TEST_KEY = 'test_value';

        const value = Env.get('TEST_KEY');

        expect(value).toBe('test_value');
    });

    it('should return the fallback value if the environment variable is not set', () => {
        const value = Env.get('NON_EXISTENT_KEY', 'fallback_value');

        expect(value).toBe('fallback_value');
    });

    it('should return null if the environment variable is not set and no fallback is provided', () => {
        const value = Env.get('NON_EXISTENT_KEY');

        expect(value).toBeNull();
    });
});