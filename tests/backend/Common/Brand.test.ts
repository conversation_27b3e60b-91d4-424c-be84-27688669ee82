import Brand from '@/backend/Common/Brand.ts';
import {beforeEach, describe, expect, it} from '@jest/globals';

describe('Brand class', () => {
    let brand;

    beforeEach(() => {
        const brandConfig = {
            slug: 'example-brand',
            google_adsense: {
                contract_type: 'online',
            },
        };
        brand = new Brand(brandConfig);
    });

    it('should return the correct slug', () => {
        const slug = brand.slug;

        expect(slug).toBe('example-brand');
    });

    it('should return "search" as subdomain when google_adsense.contract_type is "online"', () => {
        brand.brandConfig.google_adsense.contract_type = 'online';

        const subdomain = brand.getSubDomain();

        expect(subdomain).toBe('search');
    });

    it('should return "www" as subdomain when google_adsense.contract_type is not "online"', () => {
        brand.brandConfig.google_adsense.contract_type = 'offline';

        const subdomain = brand.getSubDomain();

        expect(subdomain).toBe('www');
    });
});