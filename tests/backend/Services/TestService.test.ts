import TestService from '@/backend/Services/TestService.ts';
import OracleConfig from '@/shared/types/Common/OracleConfig.ts';
import Brand from '@/backend/Common/Brand.ts';
import {describe, expect, it, jest, beforeEach} from '@jest/globals';

// Mock dependencies
jest.mock('@/backend/Test/FrontendTestRunner.ts');
jest.mock('@/backend/Common/Log.ts');

describe('TestService', () => {
    let testService: TestService;
    let mockOracleConfig: OracleConfig;

    beforeEach(() => {
        testService = new TestService();
        
        // Create mock oracle config
        const mockBrand = new Brand({
            slug: 'test-brand',
            domains: [
                { host: 'www.test.com', locale: 'en' }
            ],
            google_adsense: {
                contract_type: 'online'
            }
        });

        mockOracleConfig = {
            brands: [mockBrand],
            urls: ['/'],
            browsers: [{
                product: 'chrome',
                executable: '/usr/bin/chrome'
            }],
            viewports: [{
                name: 'desktop',
                width: 1920,
                height: 1080
            }],
            drivers: ['puppeteer'],
            user: 'test-user'
        } as OracleConfig;
    });

    describe('constructor', () => {
        it('should create TestService with default config', () => {
            expect(testService).toBeInstanceOf(TestService);
            expect(testService.isRunning()).toBe(false);
        });

        it('should create TestService with custom config', () => {
            const customService = new TestService({
                environments: ['staging', 'production'],
                maxConcurrency: 10,
                cleanupAfterRun: false,
            });

            const config = customService.getConfig();
            expect(config.environments).toEqual(['staging', 'production']);
            expect(config.maxConcurrency).toBe(10);
            expect(config.cleanupAfterRun).toBe(false);
        });
    });

    describe('validateConfig', () => {
        it('should validate valid Oracle config', () => {
            const result = testService.validateConfig(mockOracleConfig);
            expect(result.valid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });

        it('should reject null config', () => {
            const result = testService.validateConfig(null as any);
            expect(result.valid).toBe(false);
            expect(result.errors).toContain('Oracle configuration is required');
        });

        it('should reject config without brands', () => {
            const invalidConfig = { ...mockOracleConfig, brands: [] };
            const result = testService.validateConfig(invalidConfig);
            expect(result.valid).toBe(false);
            expect(result.errors).toContain('At least one brand must be configured');
        });

        it('should reject config without urls', () => {
            const invalidConfig = { ...mockOracleConfig, urls: [] };
            const result = testService.validateConfig(invalidConfig);
            expect(result.valid).toBe(false);
            expect(result.errors).toContain('At least one URL must be configured');
        });

        it('should reject config without user', () => {
            const invalidConfig = { ...mockOracleConfig, user: '' };
            const result = testService.validateConfig(invalidConfig);
            expect(result.valid).toBe(false);
            expect(result.errors).toContain('User must be specified');
        });
    });

    describe('estimateExecutionTime', () => {
        it('should estimate execution time for valid config', () => {
            const estimatedTime = testService.estimateExecutionTime(mockOracleConfig);
            
            // Expected: 1 brand * 1 url * 1 browser * 1 viewport * 1 driver * 2 environments * 5000ms
            const expected = 1 * 1 * 1 * 1 * 1 * 2 * 5000;
            expect(estimatedTime).toBe(expected);
        });

        it('should return 0 for null config', () => {
            const estimatedTime = testService.estimateExecutionTime(null as any);
            expect(estimatedTime).toBe(0);
        });

        it('should calculate correctly for multiple items', () => {
            const configWithMultiple = {
                ...mockOracleConfig,
                brands: [mockOracleConfig.brands[0], mockOracleConfig.brands[0]], // 2 brands
                urls: ['/', '/search'], // 2 urls
            };

            const estimatedTime = testService.estimateExecutionTime(configWithMultiple);
            
            // Expected: 2 brands * 2 urls * 1 browser * 1 viewport * 1 driver * 2 environments * 5000ms
            const expected = 2 * 2 * 1 * 1 * 1 * 2 * 5000;
            expect(estimatedTime).toBe(expected);
        });
    });

    describe('getStatus', () => {
        it('should return initial status', () => {
            const status = testService.getStatus();
            expect(status.isRunning).toBe(false);
            expect(status.currentRun).toBeUndefined();
        });
    });

    describe('getConfig', () => {
        it('should return readonly config', () => {
            const config = testService.getConfig();
            expect(config.environments).toEqual(['production', 'local']);
            expect(config.maxConcurrency).toBe(25);
            expect(config.cleanupAfterRun).toBe(true);
        });
    });

    describe('isRunning', () => {
        it('should return false initially', () => {
            expect(testService.isRunning()).toBe(false);
        });
    });

    describe('getCurrentRunInfo', () => {
        it('should return undefined when not running', () => {
            expect(testService.getCurrentRunInfo()).toBeUndefined();
        });
    });
});
