import TestCase from '@/backend/Test/TestCase.ts';
import TestCaseFactory from '@/backend/Test/TestCaseFactory.ts';
import UrlSetFactory from '@/backend/Test/UrlSetFactory.ts';
import browsers from '@/shared/config/browsers.ts';
import viewports from '@/shared/config/viewports.ts';
import {DriverFactory} from '@/backend/Test/Driver/DriverFactory.ts';

export class TestCaseStubs {
    static get(): TestCase[] {
        const date = '2025-02-26';

        return [
            TestCaseFactory.create(
                date,
                'seekweb',
                UrlSetFactory.create(
                    'www.seekweb.com',
                    '/',
                ),
                browsers.find((browser) => browser.product === 'chrome'),
                viewports.find((viewport) => viewport.name === 'desktop'),
                DriverFactory.createDriver('puppeteer'),
                'jest',
            ),
            TestCaseFactory.create(
                date,
                'zapmeta',
                UrlSetFactory.create(
                    'www.zapmeta.com',
                    '/dsr',
                ),
                browsers.find((browser) => browser.product === 'chrome'),
                viewports.find((viewport) => viewport.name === 'desktop'),
                DriverFactory.createDriver('puppeteer'),
                'jest',
            ),
        ];
    }
}
