module.exports = {
    presets: [
        ['@babel/preset-env', { targets: { node: 'current' } }],
        '@babel/preset-typescript',
    ],
    env: {
        test: {
            plugins: [
                "@babel/plugin-transform-modules-commonjs",
                "@babel/plugin-transform-private-methods",
                ['@babel/plugin-proposal-decorators', { decoratorsBeforeExport: true }],
            ]
        }
    }
}
