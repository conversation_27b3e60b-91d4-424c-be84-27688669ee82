#!/bin/bash

GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
RESET='\033[0m' # Reset color

echo -e "${YELLOW}Removing any existing data...${RESET}"
rm -rf public/screenshots public/logs || {
    echo -e "${RED}Unable to remove existing data! Exiting...${RESET}"
    exit 1
}

echo -e "${YELLOW}Installing dependencies with Yarn...${RESET}"
yarn || {
    echo -e "${RED}Yarn install failed! Exiting...${RESET}"
    exit 1
}

echo -e "${YELLOW}Syncing git hooks...${RESET}"
yarn lefthook install || {
    echo -e "${RED}Lefthook install failed! Exiting...${RESET}"
    exit 1
}

echo -e "${YELLOW}Running git checkout hook...${RESET}"
yarn lefthook run post-checkout || {
    echo -e "${RED}Post checkout hook failed! Exiting...${RESET}"
    exit 1
}

echo -e "${YELLOW}Updating .env...${RESET}"
node --import tsx scripts/update-env.ts || {
    echo -e "${RED}Failed updating .env! Exiting...${RESET}"
    exit 1
}

echo -e "${GREEN}Done updating Oracle!${RESET}"