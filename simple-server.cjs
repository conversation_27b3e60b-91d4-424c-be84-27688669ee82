const express = require('express');
const path = require('path');
const { createServer } = require('http');

const app = express();
const server = createServer(app);
const PORT = process.env.PORT || 3000;

console.log('🚀 Starting Oracle server...');

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// CORS middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Request logging
app.use((req, res, next) => {
  console.log(`${req.method} ${req.path} - ${req.ip}`);
  next();
});

// API routes
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Oracle API is running',
    timestamp: new Date().toISOString(),
  });
});

app.post('/api/run-test', (req, res) => {
  console.log('Test run request received:', req.body);
  
  // Simulate test execution
  setTimeout(() => {
    res.json({
      success: true,
      data: {
        date: new Date().toISOString(),
        totalTestCases: Math.floor(Math.random() * 50) + 10,
        executionTime: Math.floor(Math.random() * 30000) + 5000,
        estimatedTime: 25000,
      },
    });
  }, 1000);
});

app.get('/api/test-status', (req, res) => {
  res.json({
    success: true,
    data: {
      status: { 
        isRunning: false,
        progress: 0,
        currentStep: '',
        startTime: null,
        estimatedTime: null,
        logs: [],
      },
      config: { 
        environments: ['local', 'production'],
        maxConcurrency: 25,
        cleanupAfterRun: true,
      },
    },
  });
});

// Serve static files from dist directory
app.use(express.static(path.join(__dirname, 'dist')));

// Serve screenshots directory
app.use('/screenshots', express.static(path.join(__dirname, 'public/screenshots')));

// Handle client-side routing - serve index.html for all non-API routes
app.get('*', (req, res) => {
  // Don't serve index.html for API routes or static assets
  if (req.path.startsWith('/api') || req.path.startsWith('/screenshots') || req.path.includes('.')) {
    res.status(404).json({ error: 'Not found' });
    return;
  }
  
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(`Express error: ${err.message}`);
  res.status(500).json({
    success: false,
    error: process.env.NODE_ENV === 'production' ? 'Internal server error' : err.message,
  });
});

// Graceful shutdown
const gracefulShutdown = () => {
  console.log('📴 Received shutdown signal, closing server gracefully...');
  
  server.close(() => {
    console.log('✅ HTTP server closed');
    process.exit(0);
  });
  
  // Force close after 10 seconds
  setTimeout(() => {
    console.error('❌ Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 10000);
};

// Handle shutdown signals
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error(`❌ Uncaught Exception: ${error.message}`);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(`❌ Unhandled Rejection: ${reason}`);
  process.exit(1);
});

// Start server
server.listen(PORT, () => {
  console.log(`🚀 Oracle server running on port ${PORT}`);
  console.log(`📱 Frontend: http://localhost:${PORT}`);
  console.log(`🔌 API: http://localhost:${PORT}/api`);
  console.log(`📁 Screenshots: http://localhost:${PORT}/screenshots`);
  console.log(`✅ Server ready!`);
  
  if (process.env.NODE_ENV !== 'production') {
    console.log('🔧 Development mode - CORS enabled for all origins');
  }
});

module.exports = app;
