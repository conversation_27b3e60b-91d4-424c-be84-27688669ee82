{"name": "oracle", "version": "0.1.0", "private": true, "type": "module", "scripts": {"serve": "vite", "build:frontend": "vite build; node --import tsx scripts/create-symlink.ts", "build:backend": "tsc -p tsconfig.json", "build": "yarn build:frontend; yarn build:backend", "app:artemis:brands": "node --import tsx src/backend/Console/ArtemisPullBrands.ts", "test:frontend": "node --import tsx src/backend/Console/FrontendTest.ts", "test": "jest --coverage --no-cache", "update": "./update-oracle.sh", "reset": "./reset-oracle.sh"}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/plugin-transform-modules-commonjs": "^7.26.3", "@babel/plugin-transform-private-methods": "^7.25.9", "@babel/preset-env": "^7.26.9", "@babel/preset-typescript": "^7.26.0", "@img-comparison-slider/vue": "^8.0.0", "@jest/globals": "^29.7.0", "@types/cli-progress": "^3.11.6", "@types/express": "^5", "@types/inquirer": "^9.0.7", "@types/jest": "^29.5.14", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.4", "@types/pixelmatch": "^5.2.6", "@types/pngjs": "^6.0.5", "@vitejs/plugin-vue": "^5.2.1", "ansis": "^3.16.0", "babel-jest": "^29.7.0", "chart.js": "^4.4.8", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-adapter-moment": "^1.0.1", "cli-progress": "^3.12.0", "csv-loader": "^3.0.5", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "express": "^5.1.0", "html-loader": "4.2.0", "inquirer": "^8.2.6", "jest": "^29.7.0", "lefthook": "^1.11.2", "lodash-es": "^4.17.21", "moment": "^2.30.1", "pinia": "^3.0.2", "pixelmatch": "^7.1.0", "playwright": "^1.50.1", "playwright-cluster": "^1.0.7", "pngjs": "^7.0.0", "puppeteer": "21.11.0", "puppeteer-cluster": "^0.24.0", "register-service-worker": "^1.7.2", "sass": "^1.85.0", "ts-jest": "^29.2.6", "tsx": "^4.19.3", "typescript": "^5.7.3", "vite": "^6.1.0", "vite-plugin-html": "^3.2.2", "vue": "^3.5.13", "vue-router": "^4.5.0", "yargs": "^17.7.2"}, "packageManager": "yarn@4.9.1"}