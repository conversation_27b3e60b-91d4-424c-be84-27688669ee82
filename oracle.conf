<VirtualHost *:80>
    ServerName oracle
    ServerAlias oracle.*.ldev.nl

    # Serve Vue frontend (built into /public)
    DocumentRoot /var/www/html/oracle/public

    <Directory /var/www/html/oracle/public>
        AllowOverride all
        Options -MultiViews
        Require all granted
    </Directory>

    # Run Node.js backend directly via Passenger
    PassengerEnabled on
    PassengerAppType node
    PassengerStartupFile api.ts
    PassengerAppRoot /var/www/html/oracle/src/backend/routes/
</VirtualHost>
