export interface RequestData {
    url: string;
    initiator?: {
        type: string;
        url: string;
    };
    loadTime?: number; // Time in milliseconds to load this resource
    startTime?: number; // Start time in milliseconds
    endTime?: number; // End time in milliseconds
}

export interface ScreenshotRequestsData {
    request?: RequestData[];
    response?: RequestData[];
}

export interface ScreenshotEnvironmentData {
    local?: {
        requests?: ScreenshotRequestsData;
        screenshot?: string;
        html?: string;
    };
    production?: {
        requests?: ScreenshotRequestsData;
        screenshot?: string;
        html?: string;
    };
}

export interface TestCaseData {
    date: string;
    filePath: string;
    urlSet: {
        url: string;
        domain: string;
        production?: string;
        local?: string;
    };
    brandSlug: string;
    driver: {
        name: string;
    };
    browser: {
        product: string;
        displayName?: string;
    };
    viewport: {
        name: string;
        width?: number;
        height?: number;
    };
    user: string;
    brandName?: string; // Added for compatibility with existing code
}

export interface PageLoadMetrics {
    domContentLoaded?: number; // Time in milliseconds for DOMContentLoaded event
    load?: number; // Time in milliseconds for load event
    firstPaint?: number; // Time in milliseconds for first paint
    firstContentfulPaint?: number; // Time in milliseconds for first contentful paint
    totalRequests?: number; // Total number of requests
    totalBytes?: number; // Total bytes transferred
}

export interface AuditItem {
    testCase: TestCaseData;
    diffAmount: number;
    passing: boolean;
    datestamp?: string; // Added by the frontend when loading from a specific directory
    screenshotResponse?: ScreenshotEnvironmentData;
    loadMetrics?: {
        local?: PageLoadMetrics;
        production?: PageLoadMetrics;
    };
}

export interface ChartUrlData {
    url: string;
    count: number;
}

export interface ChartPointData {
    date: string;
    count: number;
    urls: Record<string, number>;
    loadTimes?: {
        url: string;
        brand: string;
        loadTime: number;
        environment: 'local' | 'production';
    }[];
    requestDiffs?: {
        url: string;
        added: number;
        removed: number;
        changed: number;
    }[];
}

export interface ChartGroupData {
    group: string;
    data: ChartPointData[];
    urlCounts: ChartUrlData[];
}

export interface ProcessedChartData {
    group: string;
    data: ChartPointData[];
    loadTimeData?: {
        url: string;
        brand: string;
        environment: 'local' | 'production';
        points: { date: string; loadTime: number }[];
    }[];
    requestDiffData?: {
        url: string;
        points: { date: string; added: number; removed: number; changed: number }[];
    }[];
}

export type GroupByOption = 'brand' | 'device' | 'driver';

export interface AnalyticsFilters {
    groupBy: GroupByOption;
    endpointFilter: string;
    showUrlDetails?: boolean;
}
