import type Route from '@/shared/types/Config/Route.ts';

const routes: Route[] = [
    {
        name: 'Home /',
        value: '/',
    },
    {
        name: '/search',
        value: '/search',
    },
    {
        name: '/ws',
        value: '/ws',
    },
    {
        name: '/wsa',
        value: '/wsa',
    },
    {
        name: '/ds',
        value: '/ds',
    },
    {
        name: '/dsa',
        value: '/dsa',
    },
    {
        name: '/dsr',
        value: '/dsr',
    },
    {
        name: '/dsrw',
        value: '/dsrw',
    },
    {
        name: '/msr',
        value: '/msr',
    },
    {
        name: '/msrw',
        value: '/msrw',
    },
    {
        name: '/cs',
        value: '/cs',
    },
    {
        name: '/csa',
        value: '/csa',
    },
    {
        name: '/ms',
        value: '/ms',
    },
];

export default routes;
