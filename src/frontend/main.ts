import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import router from './router/index.ts';

// Create Pinia instance with dev tools
const pinia = createPinia();

// Enable Pinia dev tools in development
if (import.meta.env.DEV) {
  // Add any dev-specific Pinia plugins here
}

// Create app
const app = createApp(App);

// Global error handler
app.config.errorHandler = (error, instance, info) => {
  console.error('Global error:', error);
  console.error('Component instance:', instance);
  console.error('Error info:', info);

  // In production, you might want to send this to an error reporting service
  if (import.meta.env.PROD) {
    // Send to error reporting service
  }
};

// Global warning handler (development only)
if (import.meta.env.DEV) {
  app.config.warnHandler = (msg, instance, trace) => {
    console.warn('Vue warning:', msg);
    console.warn('Component trace:', trace);
  };
}

// Use plugins
app.use(pinia);
app.use(router);

// Global properties (if needed)
app.config.globalProperties.$env = import.meta.env;

// Mount app
app.mount('#app');
