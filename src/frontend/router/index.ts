import {createRouter, createWebHistory, RouteRecordRaw, RouteRecordRedirect} from 'vue-router';

const routes: [RouteRecordRaw|RouteRecordRedirect] = [
    {
        path: '/',
        name: 'home',
        component: () => import('../views/HomeView.vue'),
        meta: {
            title: 'Oracle - Screenshot Tests',
        },
    },
    {
        path: '/screenshots',
        redirect: to => ({
            path: '/',
            query: {view: 'analytics', ...to.query},
        }),
    },
    {
        path: '/analytics',
        redirect: to => ({
            path: '/',
            query: {view: 'analytics', ...to.query},
        }),
    },
];

const router = createRouter({
    history: createWebHistory(import.meta!.env.BASE_URL),
    routes: routes,
    // Scroll to top on navigation
    scrollBehavior(to, from, savedPosition) {
        if (savedPosition) {
            return savedPosition;
        } else {
            return {top: 0};
        }
    },
});

router.afterEach((to) => {
    document.title = to.meta.title as string || 'Oracle';
});

export default router;
