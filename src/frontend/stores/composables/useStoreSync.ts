import { watch, onMounted, onUnmounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useScreenshotStore } from '../modules/screenshotStore.ts';
import { useAnalyticsStore } from '../modules/analyticsStore.ts';
import { useAppStore } from '../modules/appStore.ts';

/**
 * Composable for syncing store state with URL parameters
 */
export function useStoreSync() {
  const router = useRouter();
  const route = useRoute();
  const screenshotStore = useScreenshotStore();
  const analyticsStore = useAnalyticsStore();
  const appStore = useAppStore();

  // Sync URL with store state
  const syncUrlWithState = () => {
    const query: Record<string, string> = {};

    // Screenshot filters
    if (screenshotStore.filters.date) query.date = screenshotStore.filters.date;
    if (screenshotStore.filters.brand) query.brand = screenshotStore.filters.brand;
    if (screenshotStore.filters.test) query.test = screenshotStore.filters.test;
    if (screenshotStore.filters.viewport) query.viewport = screenshotStore.filters.viewport;
    if (screenshotStore.filters.browser) query.browser = screenshotStore.filters.browser;
    if (screenshotStore.filters.driver) query.driver = screenshotStore.filters.driver;
    if (screenshotStore.filters.domain) query.domain = screenshotStore.filters.domain;

    // View state
    if (screenshotStore.view.type !== 'thumbnail') query.view = screenshotStore.view.type;
    if (screenshotStore.view.diffVisible) query.diff = 'true';
    if (screenshotStore.view.selectedItem) query.selected = screenshotStore.view.selectedItem;

    // Analytics state
    if (analyticsStore.state.groupBy !== 'brand') query.groupBy = analyticsStore.state.groupBy;
    if (!analyticsStore.state.showUrlDetails) query.showUrlDetails = 'false';
    if (analyticsStore.state.selectedEnvironment !== 'production') {
      query.environment = analyticsStore.state.selectedEnvironment;
    }

    // Pagination
    if (screenshotStore.pagination.page > 1) query.page = screenshotStore.pagination.page.toString();
    if (screenshotStore.pagination.pageSize !== 20) {
      query.pageSize = screenshotStore.pagination.pageSize.toString();
    }

    // Update URL without triggering navigation
    router.replace({ query }).catch(() => {
      // Ignore navigation errors (e.g., duplicate navigation)
    });
  };

  // Sync store state with URL
  const syncStateWithUrl = () => {
    const query = route.query;

    // Update screenshot filters
    screenshotStore.updateFilters({
      date: query.date as string || null,
      brand: query.brand as string || null,
      test: query.test as string || null,
      viewport: query.viewport as string || null,
      browser: query.browser as string || null,
      driver: query.driver as string || null,
      domain: query.domain as string || null,
    });

    // Update view state
    screenshotStore.updateView({
      type: (query.view as any) || 'thumbnail',
      diffVisible: query.diff === 'true',
      selectedItem: query.selected as string || null,
    });

    // Update analytics state
    analyticsStore.updateState({
      groupBy: (query.groupBy as any) || 'brand',
      showUrlDetails: query.showUrlDetails !== 'false',
      selectedEnvironment: (query.environment as any) || 'production',
    });

    // Update pagination
    screenshotStore.updatePagination({
      page: parseInt(query.page as string) || 1,
      pageSize: parseInt(query.pageSize as string) || 20,
    });
  };

  // Initialize from URL on mount
  const initializeFromUrl = async () => {
    try {
      appStore.setLoading(true);
      
      // Sync state with current URL
      syncStateWithUrl();
      
      // Load initial data if date is specified
      if (route.query.date) {
        await screenshotStore.fetchConfig(route.query.date as string);
      } else {
        // Load directories to get available dates
        await screenshotStore.fetchDirectories();
        
        // If we have dates available, load the most recent one
        if (screenshotStore.performedTests.length > 0) {
          const mostRecentDate = screenshotStore.performedTests[0];
          await screenshotStore.fetchConfig(mostRecentDate);
          
          // Update URL with the loaded date
          screenshotStore.updateFilters({ date: mostRecentDate });
        }
      }
      
      appStore.setLoading(false);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to initialize from URL';
      appStore.setLoading(false, errorMessage);
      appStore.showErrorNotification('Initialization Error', errorMessage);
    }
  };

  // Watch for store changes and sync to URL
  const stopWatchers: Array<() => void> = [];

  const startWatching = () => {
    // Watch screenshot filters
    stopWatchers.push(
      watch(() => screenshotStore.filters, syncUrlWithState, { deep: true })
    );

    // Watch view state
    stopWatchers.push(
      watch(() => screenshotStore.view, syncUrlWithState, { deep: true })
    );

    // Watch analytics state
    stopWatchers.push(
      watch(() => analyticsStore.state, syncUrlWithState, { deep: true })
    );

    // Watch pagination
    stopWatchers.push(
      watch(() => screenshotStore.pagination, syncUrlWithState, { deep: true })
    );
  };

  const stopWatching = () => {
    stopWatchers.forEach(stop => stop());
    stopWatchers.length = 0;
  };

  // Lifecycle hooks
  onMounted(() => {
    initializeFromUrl();
    startWatching();
  });

  onUnmounted(() => {
    stopWatching();
  });

  return {
    syncUrlWithState,
    syncStateWithUrl,
    initializeFromUrl,
    startWatching,
    stopWatching,
  };
}

/**
 * Composable for manual URL state management
 */
export function useUrlStateManagement() {
  const router = useRouter();
  const route = useRoute();

  const updateUrlQuery = (updates: Record<string, string | null>) => {
    const query = { ...route.query };

    // Apply updates
    Object.entries(updates).forEach(([key, value]) => {
      if (value === null || value === undefined || value === '') {
        delete query[key];
      } else {
        query[key] = value;
      }
    });

    // Update URL
    router.replace({ query }).catch(() => {
      // Ignore navigation errors
    });
  };

  const clearUrlQuery = () => {
    router.replace({ query: {} }).catch(() => {
      // Ignore navigation errors
    });
  };

  const getQueryParam = (key: string, defaultValue: string | null = null): string | null => {
    const value = route.query[key];
    if (Array.isArray(value)) {
      return value[0] || defaultValue;
    }
    return value || defaultValue;
  };

  const hasQueryParam = (key: string): boolean => {
    return key in route.query;
  };

  return {
    updateUrlQuery,
    clearUrlQuery,
    getQueryParam,
    hasQueryParam,
    currentQuery: computed(() => route.query),
  };
}
