import { computed } from 'vue';
import { useAppStore } from '../modules/appStore.ts';
import { useScreenshotStore } from '../modules/screenshotStore.ts';
import { useAnalyticsStore } from '../modules/analyticsStore.ts';
import { useTestRunStore } from '../modules/testRunStore.ts';

/**
 * Composable for accessing combined store state
 */
export function useStoreState() {
  const appStore = useAppStore();
  const screenshotStore = useScreenshotStore();
  const analyticsStore = useAnalyticsStore();
  const testRunStore = useTestRunStore();

  // Combined loading state
  const isLoading = computed(() => 
    appStore.isLoading || 
    screenshotStore.isLoading || 
    analyticsStore.isLoading || 
    testRunStore.isLoading
  );

  // Combined error state
  const hasError = computed(() => 
    appStore.hasError || 
    screenshotStore.hasError || 
    analyticsStore.hasError || 
    testRunStore.hasError
  );

  const currentErrors = computed(() => {
    const errors: string[] = [];
    
    if (appStore.currentError) errors.push(`App: ${appStore.currentError}`);
    if (screenshotStore.currentError) errors.push(`Screenshots: ${screenshotStore.currentError}`);
    if (analyticsStore.currentError) errors.push(`Analytics: ${analyticsStore.currentError}`);
    if (testRunStore.currentError) errors.push(`Test Run: ${testRunStore.currentError}`);
    
    return errors;
  });

  // Data availability
  const hasData = computed(() => 
    screenshotStore.hasData || analyticsStore.hasData
  );

  const isEmpty = computed(() => 
    !isLoading.value && !hasData.value
  );

  // Current view context
  const currentView = computed(() => screenshotStore.view.type);
  
  const isAnalyticsView = computed(() => currentView.value === 'analytics');
  const isRunTestView = computed(() => currentView.value === 'run-test');
  const isIndividualView = computed(() => currentView.value === 'individual');
  const isThumbnailView = computed(() => currentView.value === 'thumbnail');

  // Test run status
  const isTestRunning = computed(() => testRunStore.isRunning);
  const testProgress = computed(() => testRunStore.progressPercentage);
  const testCurrentStep = computed(() => testRunStore.state.currentStep);

  // Data counts
  const totalAuditItems = computed(() => screenshotStore.audit.length);
  const filteredAuditItems = computed(() => screenshotStore.filteredAudit.length);
  const availableDates = computed(() => screenshotStore.performedTests.length);

  // Filter status
  const hasActiveFilters = computed(() => {
    const filters = screenshotStore.filters;
    return !!(
      filters.brand || 
      filters.test || 
      filters.viewport || 
      filters.browser || 
      filters.driver || 
      filters.domain
    );
  });

  const activeFilterCount = computed(() => {
    const filters = screenshotStore.filters;
    let count = 0;
    
    if (filters.brand) count++;
    if (filters.test) count++;
    if (filters.viewport) count++;
    if (filters.browser) count++;
    if (filters.driver) count++;
    if (filters.domain) count++;
    
    return count;
  });

  // Pagination status
  const currentPage = computed(() => screenshotStore.pagination.page);
  const totalPages = computed(() => 
    Math.ceil(screenshotStore.filteredAudit.length / screenshotStore.pagination.pageSize)
  );
  const hasNextPage = computed(() => currentPage.value < totalPages.value);
  const hasPreviousPage = computed(() => currentPage.value > 1);

  // Analytics status
  const analyticsGroupBy = computed(() => analyticsStore.state.groupBy);
  const analyticsEnvironment = computed(() => analyticsStore.state.selectedEnvironment);
  const hasAnalyticsData = computed(() => analyticsStore.hasData);

  // Notifications
  const hasNotifications = computed(() => appStore.hasNotifications);
  const notificationCount = computed(() => appStore.notifications.notifications.length);
  const unreadNotificationCount = computed(() => appStore.unreadNotifications.length);

  // User preferences
  const userTheme = computed(() => appStore.userPreferences.theme);
  const autoRefresh = computed(() => appStore.userPreferences.autoRefresh);
  const defaultView = computed(() => appStore.userPreferences.defaultView);

  // Navigation
  const currentRoute = computed(() => appStore.navigation.currentRoute);
  const breadcrumbs = computed(() => appStore.navigation.breadcrumbs);
  const sidebarOpen = computed(() => appStore.navigation.sidebarOpen);

  // Summary statistics
  const summary = computed(() => ({
    totalTests: totalAuditItems.value,
    filteredTests: filteredAuditItems.value,
    availableDates: availableDates.value,
    activeFilters: activeFilterCount.value,
    currentPage: currentPage.value,
    totalPages: totalPages.value,
    isLoading: isLoading.value,
    hasError: hasError.value,
    hasData: hasData.value,
    currentView: currentView.value,
    isTestRunning: isTestRunning.value,
    testProgress: testProgress.value,
  }));

  // Quick access to commonly used data
  const quickAccess = computed(() => ({
    // Most recent test date
    latestDate: screenshotStore.performedTests[0] || null,
    
    // Selected test case
    selectedTestCase: screenshotStore.selectedTestCase,
    
    // Available filter options
    brands: screenshotStore.availableBrands,
    tests: screenshotStore.availableTests,
    viewports: screenshotStore.availableViewports,
    browsers: screenshotStore.availableBrowsers,
    drivers: screenshotStore.availableDrivers,
    domains: screenshotStore.availableDomains,
    
    // Current filters
    currentFilters: screenshotStore.filters,
    
    // Analytics data
    analyticsData: analyticsStore.processedData,
    
    // Test run config
    testRunConfig: testRunStore.config,
    
    // Last test result
    lastTestResult: testRunStore.lastResult,
  }));

  return {
    // Combined state
    isLoading,
    hasError,
    currentErrors,
    hasData,
    isEmpty,

    // View state
    currentView,
    isAnalyticsView,
    isRunTestView,
    isIndividualView,
    isThumbnailView,

    // Test run state
    isTestRunning,
    testProgress,
    testCurrentStep,

    // Data counts
    totalAuditItems,
    filteredAuditItems,
    availableDates,

    // Filter state
    hasActiveFilters,
    activeFilterCount,

    // Pagination state
    currentPage,
    totalPages,
    hasNextPage,
    hasPreviousPage,

    // Analytics state
    analyticsGroupBy,
    analyticsEnvironment,
    hasAnalyticsData,

    // Notifications
    hasNotifications,
    notificationCount,
    unreadNotificationCount,

    // User preferences
    userTheme,
    autoRefresh,
    defaultView,

    // Navigation
    currentRoute,
    breadcrumbs,
    sidebarOpen,

    // Summary and quick access
    summary,
    quickAccess,
  };
}

/**
 * Composable for accessing specific store state
 */
export function useSpecificStoreState() {
  return {
    app: useAppStore(),
    screenshots: useScreenshotStore(),
    analytics: useAnalyticsStore(),
    testRun: useTestRunStore(),
  };
}
