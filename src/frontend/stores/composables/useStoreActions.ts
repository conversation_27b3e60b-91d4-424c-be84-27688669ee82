import { ref, readonly } from 'vue';
import { useAppStore } from '../modules/appStore.ts';
import { useScreenshotStore } from '../modules/screenshotStore.ts';
import { useAnalyticsStore } from '../modules/analyticsStore.ts';
import { useTestRunStore } from '../modules/testRunStore.ts';
import type { ActionResult } from '../types/index.ts';

/**
 * Composable for common store actions with error handling
 */
export function useStoreActions() {
  const appStore = useAppStore();
  const screenshotStore = useScreenshotStore();
  const analyticsStore = useAnalyticsStore();
  const testRunStore = useTestRunStore();

  const isExecuting = ref(false);
  const lastError = ref<string | null>(null);

  // Generic action executor with error handling
  const executeAction = async <T>(
    action: () => Promise<ActionResult<T>>,
    successMessage?: string,
    errorMessage?: string
  ): Promise<ActionResult<T>> => {
    if (isExecuting.value) {
      return {
        success: false,
        error: 'Another action is already in progress',
        timestamp: new Date(),
      };
    }

    try {
      isExecuting.value = true;
      lastError.value = null;

      const result = await action();

      if (result.success) {
        if (successMessage) {
          appStore.showSuccessNotification('Success', successMessage);
        }
      } else {
        lastError.value = result.error || 'Unknown error';
        if (errorMessage) {
          appStore.showErrorNotification('Error', errorMessage);
        }
      }

      return result;
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error occurred';
      lastError.value = errorMsg;
      
      if (errorMessage) {
        appStore.showErrorNotification('Error', errorMessage);
      }

      return {
        success: false,
        error: errorMsg,
        timestamp: new Date(),
      };
    } finally {
      isExecuting.value = false;
    }
  };

  // Screenshot actions
  const loadScreenshotData = async (date: string): Promise<ActionResult> => {
    return executeAction(
      () => screenshotStore.fetchConfig(date),
      `Loaded screenshot data for ${date}`,
      'Failed to load screenshot data'
    );
  };

  const refreshScreenshotDirectories = async (): Promise<ActionResult> => {
    return executeAction(
      () => screenshotStore.fetchDirectories(),
      'Refreshed available test dates',
      'Failed to refresh test directories'
    );
  };

  // Analytics actions
  const loadAnalyticsData = async (): Promise<ActionResult> => {
    return executeAction(
      () => analyticsStore.loadAllAnalyticsData(),
      'Loaded analytics data',
      'Failed to load analytics data'
    );
  };

  const processAnalyticsData = async (): Promise<ActionResult> => {
    return executeAction(
      () => analyticsStore.processData(),
      'Processed analytics data',
      'Failed to process analytics data'
    );
  };

  const exportAnalyticsData = async (format: 'json' | 'csv' = 'json'): Promise<ActionResult> => {
    return executeAction(
      () => Promise.resolve(analyticsStore.exportData(format)),
      `Exported analytics data as ${format.toUpperCase()}`,
      'Failed to export analytics data'
    );
  };

  // Test run actions
  const runTest = async (): Promise<ActionResult> => {
    return executeAction(
      () => testRunStore.runTest(),
      'Test run completed successfully',
      'Test run failed'
    );
  };

  const stopTest = async (): Promise<ActionResult> => {
    return executeAction(
      () => testRunStore.stopTest(),
      'Test run stopped',
      'Failed to stop test run'
    );
  };

  // Batch actions
  const refreshAllData = async (): Promise<ActionResult> => {
    try {
      isExecuting.value = true;
      appStore.setLoading(true);

      // Refresh directories first
      const directoriesResult = await screenshotStore.fetchDirectories();
      if (!directoriesResult.success) {
        throw new Error(directoriesResult.error || 'Failed to refresh directories');
      }

      // Load most recent screenshot data if available
      if (screenshotStore.performedTests.length > 0) {
        const mostRecentDate = screenshotStore.performedTests[0];
        const configResult = await screenshotStore.fetchConfig(mostRecentDate);
        if (!configResult.success) {
          console.warn('Failed to load most recent config:', configResult.error);
        }
      }

      // Load analytics data
      const analyticsResult = await analyticsStore.loadAllAnalyticsData();
      if (!analyticsResult.success) {
        console.warn('Failed to load analytics data:', analyticsResult.error);
      }

      appStore.setLoading(false);
      appStore.showSuccessNotification('Success', 'All data refreshed successfully');

      return {
        success: true,
        timestamp: new Date(),
      };
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Failed to refresh data';
      appStore.setLoading(false, errorMsg);
      appStore.showErrorNotification('Error', 'Failed to refresh all data');

      return {
        success: false,
        error: errorMsg,
        timestamp: new Date(),
      };
    } finally {
      isExecuting.value = false;
    }
  };

  const clearAllData = (): ActionResult => {
    try {
      // Clear screenshot data
      screenshotStore.clearFilters();
      
      // Clear analytics data
      analyticsStore.clearDateRange();
      
      // Clear test run data
      testRunStore.clearResults();
      testRunStore.resetState();

      // Clear app notifications
      appStore.clearAllNotifications();
      appStore.clearError();

      appStore.showInfoNotification('Cleared', 'All data and filters cleared');

      return {
        success: true,
        timestamp: new Date(),
      };
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Failed to clear data';
      
      return {
        success: false,
        error: errorMsg,
        timestamp: new Date(),
      };
    }
  };

  // Utility actions
  const retryLastAction = async (): Promise<ActionResult> => {
    // This would need to be implemented based on specific requirements
    // For now, just refresh all data
    return refreshAllData();
  };

  const clearError = (): void => {
    lastError.value = null;
    appStore.clearError();
    screenshotStore.clearError();
    analyticsStore.clearError();
    testRunStore.clearError();
  };

  return {
    // State
    isExecuting: readonly(isExecuting),
    lastError: readonly(lastError),

    // Generic action executor
    executeAction,

    // Screenshot actions
    loadScreenshotData,
    refreshScreenshotDirectories,

    // Analytics actions
    loadAnalyticsData,
    processAnalyticsData,
    exportAnalyticsData,

    // Test run actions
    runTest,
    stopTest,

    // Batch actions
    refreshAllData,
    clearAllData,

    // Utility actions
    retryLastAction,
    clearError,
  };
}
