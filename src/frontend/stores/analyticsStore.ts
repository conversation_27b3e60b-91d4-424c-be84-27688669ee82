import {defineStore} from 'pinia';
import {computed, ref} from 'vue';
import type {GroupByOption, ProcessedChartData, RequestData} from '@/shared/types/Screenshot/AnalyticsTypes.ts';
import {useScreenshotStore} from '@/frontend/stores/screenshotStore.ts';

export const useAnalyticsStore = defineStore('analytics', () => {
    // State
    const groupBy = ref<GroupByOption>('brand');
    const showUrlDetails = ref(true);
    const loading = ref(false);
    const selectedEnvironment = ref<'local' | 'production'>('production');

    // Get data from screenshot store
    const screenshotStore = useScreenshotStore();
    const auditData = computed(() => screenshotStore.audit || []);

    // Computed properties
    const processedData = computed<ProcessedChartData[]>(() => {
        if (!auditData.value || auditData.value.length === 0) {
            console.log('No audit data available');
            return [];
        }

        console.log(`Processing ${auditData.value.length} audit items`);

        // Log some sample data to help debug
        if (auditData.value.length > 0) {
            console.log('Sample audit item:', JSON.stringify(auditData.value[0], null, 2));
        }

        // Include all items regardless of screenshot response
        const filteredData = auditData.value;
        console.log(`Using all ${filteredData.length} items for analytics`);

        // Group the data by date and the selected grouping criteria
        const groupedData: Record<string, Record<string, {
            count: number,
            urls: Record<string, number>,
            loadTimes: {
                url: string;
                brand: string;
                loadTime: number;
                environment: 'local' | 'production';
            }[],
            requestDiffs: {
                url: string;
                added: number;
                removed: number;
                changed: number;
            }[]
        }>> = {};

        // Track request data across dates for comparison
        const requestDataByDateAndUrl: Record<string, Record<string, RequestData[]>> = {};

        try {
            // First pass: collect request data by date and URL for comparison
            filteredData.forEach(item => {
                if (!item || !item.datestamp) {
                    return;
                }

                const date = item.datestamp;
                const url = item.testCase?.urlSet?.url || 'unknown-url';

                if (!requestDataByDateAndUrl[date]) {
                    requestDataByDateAndUrl[date] = {};
                }

                if (!requestDataByDateAndUrl[date][url]) {
                    requestDataByDateAndUrl[date][url] = [];
                }

                // Collect request data for this date and URL
                const localRequests = item.screenshotResponse?.local?.requests?.request || [];
                const productionRequests = item.screenshotResponse?.production?.requests?.request || [];

                requestDataByDateAndUrl[date][url] = [
                    ...requestDataByDateAndUrl[date][url],
                    ...(selectedEnvironment.value === 'local' ? localRequests : productionRequests),
                ];
            });

            // Second pass: process the data
            filteredData.forEach(item => {
                if (!item || !item.datestamp) {
                    return;
                }

                const date = item.datestamp;
                let groupKey: string = 'unknown';

                try {
                    if (item.testCase) {
                        switch (groupBy.value) {
                            case 'brand':
                                groupKey = item.testCase.brandSlug || 'unknown';
                                break;
                            case 'device':
                                groupKey = item.testCase.viewport?.name || 'unknown';
                                break;
                            case 'driver':
                                groupKey = item.testCase.driver?.name || 'unknown';
                                break;
                            default:
                                groupKey = 'unknown';
                        }
                    }
                } catch (error) {
                    console.warn('Error determining group key:', error);
                }

                // Initialize group if it doesn't exist
                if (!groupedData[groupKey]) {
                    groupedData[groupKey] = {};
                }

                // Initialize date if it doesn't exist
                if (!groupedData[groupKey][date]) {
                    groupedData[groupKey][date] = {
                        count: 0,
                        urls: {},
                        loadTimes: [],
                        requestDiffs: [],
                    };
                }

                // Always increment the count for this group/date
                groupedData[groupKey][date].count++;

                // Use the test URL as the default
                const url = item.testCase?.urlSet?.url || 'unknown-url';
                if (!groupedData[groupKey][date].urls[url]) {
                    groupedData[groupKey][date].urls[url] = 0;
                }
                groupedData[groupKey][date].urls[url]++;

                // Process load times
                const environment = selectedEnvironment.value;
                const metrics = item.loadMetrics?.[environment];
                const brand = item.testCase?.brandSlug || 'unknown';

                if (metrics && metrics.load) {
                    groupedData[groupKey][date].loadTimes.push({
                        url,
                        brand,
                        loadTime: metrics.load,
                        environment,
                    });
                }

                // Process request diffs
                // Compare current date's requests with previous date's requests
                const dates = Object.keys(requestDataByDateAndUrl).sort();
                const dateIndex = dates.indexOf(date);

                if (dateIndex > 0) {
                    const prevDate = dates[dateIndex - 1];
                    const currentRequests = requestDataByDateAndUrl[date][url] || [];
                    const prevRequests = requestDataByDateAndUrl[prevDate][url] || [];

                    // Simple diff: count added, removed, and changed requests
                    const currentUrls = new Set(currentRequests.map(r => r.url));
                    const prevUrls = new Set(prevRequests.map(r => r.url));

                    const added = [...currentUrls].filter(u => !prevUrls.has(u)).length;
                    const removed = [...prevUrls].filter(u => !currentUrls.has(u)).length;

                    // Count changed requests (same URL but different properties)
                    let changed = 0;
                    const commonUrls = [...currentUrls].filter(u => prevUrls.has(u));

                    commonUrls.forEach(commonUrl => {
                        const currentReq = currentRequests.find(r => r.url === commonUrl);
                        const prevReq = prevRequests.find(r => r.url === commonUrl);

                        if (currentReq && prevReq) {
                            // Check if any property changed
                            if (JSON.stringify(currentReq) !== JSON.stringify(prevReq)) {
                                changed++;
                            }
                        }
                    });

                    groupedData[groupKey][date].requestDiffs.push({
                        url,
                        added,
                        removed,
                        changed,
                    });
                }
            });
        } catch (error) {
            console.error('Error processing filtered data:', error);
            return [];
        }

        // Convert the grouped data to the format expected by the charts
        const result: ProcessedChartData[] = [];

        // Process each group
        Object.entries(groupedData).forEach(([group, dates]) => {
            const dataPoints = Object.entries(dates).map(([date, data]) => ({
                date,
                count: data.count,
                urls: data.urls,
                loadTimes: data.loadTimes,
                requestDiffs: data.requestDiffs,
            }));

            // Process load time data for line charts
            const loadTimeData: {
                url: string;
                brand: string;
                environment: 'local' | 'production';
                points: { date: string; loadTime: number }[];
            }[] = [];

            // Collect all unique URL/brand combinations
            const urlBrandCombos = new Set<string>();

            Object.entries(dates).forEach(([date, data]) => {
                data.loadTimes.forEach(lt => {
                    urlBrandCombos.add(`${lt.url}|${lt.brand}`);
                });
            });

            // Create a series for each URL/brand combination
            [...urlBrandCombos].forEach(combo => {
                const [url, brand] = combo.split('|');
                const points: { date: string; loadTime: number }[] = [];

                Object.entries(dates).forEach(([date, data]) => {
                    const loadTime = data.loadTimes.find(lt => lt.url === url && lt.brand === brand);
                    if (loadTime) {
                        points.push({
                            date,
                            loadTime: loadTime.loadTime,
                        });
                    }
                });

                if (points.length > 0) {
                    loadTimeData.push({
                        url,
                        brand,
                        environment: selectedEnvironment.value,
                        points: points.sort((a, b) => a.date.localeCompare(b.date)),
                    });
                }
            });

            // Process request diff data for stacked bar charts
            const requestDiffData: {
                url: string;
                points: { date: string; added: number; removed: number; changed: number }[];
            }[] = [];

            // Collect all unique URLs
            const uniqueUrls = new Set<string>();

            Object.entries(dates).forEach(([date, data]) => {
                data.requestDiffs.forEach(diff => {
                    uniqueUrls.add(diff.url);
                });
            });

            // Create a series for each URL
            [...uniqueUrls].forEach(url => {
                const points: { date: string; added: number; removed: number; changed: number }[] = [];

                Object.entries(dates).forEach(([date, data]) => {
                    const diff = data.requestDiffs.find(d => d.url === url);
                    if (diff) {
                        points.push({
                            date,
                            added: diff.added,
                            removed: diff.removed,
                            changed: diff.changed,
                        });
                    }
                });

                if (points.length > 0) {
                    requestDiffData.push({
                        url,
                        points: points.sort((a, b) => a.date.localeCompare(b.date)),
                    });
                }
            });

            result.push({
                group,
                data: dataPoints.sort((a, b) => a.date.localeCompare(b.date)),
                loadTimeData,
                requestDiffData,
            });
        });

        // Sort groups by total count (descending)
        result.sort((a, b) => {
            const totalA = a.data.reduce((sum, point) => sum + point.count, 0);
            const totalB = b.data.reduce((sum, point) => sum + point.count, 0);
            return totalB - totalA;
        });

        return result;
    });

    // Methods
    const loadData = async () => {
        if (loading.value) {
            console.log('Already loading analytics data, skipping loadData call');
            return Promise.resolve();
        }

        loading.value = true;

        try {
            // Load all audit data from the screenshot store
            await screenshotStore.loadAllAuditData();
            console.log(`Loaded ${auditData.value.length} audit items for analytics`);
        } catch (error) {
            console.error('Error loading audit data:', error);
        } finally {
            loading.value = false;
        }

        return Promise.resolve();
    };

    const shareUrl = () => {
        // Create URL with current filters
        const url = new URL(window.location.href);

        // Update query parameters
        url.searchParams.set('view', 'analytics');
        url.searchParams.set('groupBy', groupBy.value);
        url.searchParams.set('environment', selectedEnvironment.value);

        if (showUrlDetails.value) {
            url.searchParams.set('showDetails', 'true');
        } else {
            url.searchParams.delete('showDetails');
        }

        // Copy to clipboard
        navigator.clipboard.writeText(url.toString())
            .then(() => {
                alert('URL copied to clipboard!');
            })
            .catch(err => {
                console.error('Could not copy URL: ', err);
                alert('URL: ' + url.toString());
            });
    };

    const initFromUrl = (params: URLSearchParams) => {
        // Set group by from URL
        const urlGroupBy = params.get('groupBy') as GroupByOption;
        if (urlGroupBy && ['brand', 'device', 'driver'].includes(urlGroupBy)) {
            groupBy.value = urlGroupBy;
        }

        // Set show URL details from URL
        const urlShowDetails = params.get('showDetails');
        if (urlShowDetails) {
            showUrlDetails.value = urlShowDetails === 'true';
        }

        // Set environment from URL
        const urlEnvironment = params.get('environment') as 'local' | 'production';
        if (urlEnvironment && ['local', 'production'].includes(urlEnvironment)) {
            selectedEnvironment.value = urlEnvironment;
        }
    };

    return {
        groupBy,
        showUrlDetails,
        selectedEnvironment,
        loading,
        processedData,
        loadData,
        shareUrl,
        initFromUrl,
    };
});
