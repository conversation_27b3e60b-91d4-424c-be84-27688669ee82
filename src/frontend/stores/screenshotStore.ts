import {defineStore} from 'pinia';
import {computed, ref, watch} from 'vue';
import type {AuditItem} from '@/shared/types/Screenshot/AnalyticsTypes.ts';

// Path to screenshot data
const SCREENSHOT_PATH = '/screenshots';

export const useScreenshotStore = defineStore('screenshot', () => {
    // State
    const performedTests = ref<string[]>([]);
    const config = ref<Record<string, any> | null>(null);
    const audit = ref<AuditItem[]>([]);
    const loading = ref(false);

    // Selected values
    const selectedDate = ref<string | null>(null);
    const selectedBrand = ref<string | null>(null);
    const selectedTest = ref<string | null>(null);
    const selectedViewport = ref<string | null>(null);
    const selectedBrowser = ref<string | null>(null);
    const selectedDriver = ref<string | null>(null);
    const selectedDomain = ref<string | null>(null);

    // UI state
    const viewType = ref<'individual' | 'thumbnail' | 'analytics' | 'run-test'>('thumbnail');
    const diffVisible = ref(false);

    // Derived state
    const brands = computed(() => {
        const brandOptions: string[] = [];
        audit.value?.forEach((auditItem) => {
            if (auditItem?.testCase?.brandSlug && !brandOptions.includes(auditItem.testCase.brandSlug)) {
                brandOptions.push(auditItem.testCase.brandSlug);
            }
        });
        return brandOptions;
    });

    const pages = computed(() => {
        if (!selectedBrand.value || !audit.value) return [];

        const pageOptions: any[] = [];
        const urls: string[] = [];

        audit.value?.forEach((auditItem) => {
            if (auditItem?.testCase?.brandSlug === selectedBrand.value
                && auditItem?.testCase?.urlSet
                && !pageOptions.includes(auditItem.testCase.urlSet)
                && !urls.includes(auditItem.testCase.urlSet.url)
            ) {
                pageOptions.push(auditItem.testCase.urlSet);
                urls.push(auditItem.testCase.urlSet.url);
            }
        });

        return pageOptions;
    });

    const viewports = computed(() => {
        if (!selectedBrand.value || !config.value) return [];

        const firstTestInBrand = config.value[selectedBrand.value]?.at(0);
        return firstTestInBrand?.viewports?.map((viewport: any) => viewport.name) || [];
    });

    const browsers = computed(() => {
        if (!selectedBrand.value || !config.value) return [];

        const firstTestInBrand = config.value[selectedBrand.value]?.at(0);
        return firstTestInBrand?.browsers?.map((browser: any) => browser.product) || [];
    });

    const drivers = computed(() => {
        if (!selectedBrand.value || !config.value) return [];

        const firstTestInBrand = config.value[selectedBrand.value]?.at(0);
        return firstTestInBrand?.drivers || [];
    });

    const domains = computed(() => {
        if (!selectedBrand.value || !audit.value) return [];

        const domainOptions: string[] = [];
        audit.value?.forEach((auditItem) => {
            if (auditItem?.testCase?.brandSlug === selectedBrand.value &&
                auditItem?.testCase?.urlSet?.domain &&
                !domainOptions.includes(auditItem.testCase.urlSet.domain)) {
                domainOptions.push(auditItem.testCase.urlSet.domain);
            }
        });
        return domainOptions;
    });

    const selectedTestCase = computed(() => {
        return audit.value?.find((auditItem) => {
            const testCase = auditItem.testCase;

            return (
                testCase.brandSlug === selectedBrand.value &&
                testCase.viewport.name === selectedViewport.value &&
                testCase.browser.product === selectedBrowser.value &&
                testCase.driver.name === selectedDriver.value &&
                testCase.urlSet.url === selectedTest.value &&
                testCase.urlSet.domain === selectedDomain.value
            );
        });
    });

    /**
     * Fetch JSON file with error handling
     */
    const fetchJsonFile = async (path: string): Promise<any> => {
        try {
            const response = await fetch(`${path}?ms=${Date.now()}`);

            // Handle 404 errors
            if (response.status === 404) {
                console.warn(`File not found: ${path}`);
                return null;
            }

            // Handle other errors
            if (!response.ok) {
                console.warn(`HTTP error! status: ${response.status} for ${path}`);
                return null;
            }

            // Parse JSON
            return await response.json();
        } catch (error) {
            console.error(`Error fetching ${path}:`, error);
            return null;
        }
    };

    // Flag to prevent multiple simultaneous data loading operations
    const isLoadingData = ref(false);

    /**
     * Fetch available test directories
     * @returns A promise that resolves when the directories are loaded
     */
    const fetchDirectories = async () => {
        // Prevent multiple simultaneous calls
        if (isLoadingData.value) {
            console.log('Already loading data, skipping fetchDirectories call');
            return Promise.resolve();
        }

        isLoadingData.value = true;
        loading.value = true;

        try {
            console.log('Fetching directories...');
            const datestampsContents = await fetchJsonFile(`${SCREENSHOT_PATH}/_datestamps.json`);

            if (Array.isArray(datestampsContents) && datestampsContents.length > 0) {
                performedTests.value = structuredClone(datestampsContents).reverse();

                // Load the first test by default
                if (performedTests.value.length > 0) {
                    await fetchConfig(performedTests.value[0]);
                }
            } else {
                console.warn('No datestamps found or invalid format');
                performedTests.value = [];
                resetState();
            }
        } catch (error) {
            console.error('Unable to load datestamps:', error);
            performedTests.value = [];
            resetState();
        } finally {
            loading.value = false;
            isLoadingData.value = false;
        }

        return Promise.resolve();
    };

    /**
     * Fetch configuration for a specific test directory
     */
    const fetchConfig = async (dir: string) => {
        if (!dir) {
            console.warn('No directory provided to fetchConfig');
            return;
        }

        // Prevent multiple simultaneous calls
        if (isLoadingData.value) {
            console.log('Already loading data, skipping fetchConfig call');
            return;
        }

        isLoadingData.value = true;
        loading.value = true;

        try {
            console.log(`Fetching config for ${dir}...`);
            selectedDate.value = dir;

            // Fetch test.json and audit.json
            const testConfig = await fetchJsonFile(`${SCREENSHOT_PATH}/${dir}/test.json`);
            const auditData = await fetchJsonFile(`${SCREENSHOT_PATH}/${dir}/audit.json`);

            // Validate test.json
            if (!testConfig || typeof testConfig !== 'object') {
                console.warn(`Invalid test.json for ${dir}`);
                config.value = {};
            } else {
                config.value = testConfig;
            }

            // Validate audit.json
            if (!Array.isArray(auditData)) {
                console.warn(`Invalid audit.json for ${dir}`);
                audit.value = [];
            } else {
                // Add datestamp to each audit item
                audit.value = auditData.map(item => ({
                    ...item,
                    datestamp: dir,
                }));
            }

            // Set default selections if not already set
            if (brands.value.length > 0 && !selectedBrand.value) {
                selectedBrand.value = brands.value[0];
            }

            console.log(`Loaded config for ${dir}: ${audit.value.length} audit items, ${brands.value.length} brands`);

            // Update URL state
            updateUrlState();
        } catch (error) {
            console.error('Error fetching config:', error);
            resetState();
        } finally {
            loading.value = false;
            isLoadingData.value = false;
        }
    };

    /**
     * Load audit data from multiple directories
     */
    const loadAllAuditData = async (): Promise<AuditItem[]> => {
        if (!performedTests.value || performedTests.value.length === 0) {
            console.warn('No performed tests available');
            return [];
        }

        // Prevent multiple simultaneous calls
        if (isLoadingData.value) {
            console.log('Already loading data, skipping loadAllAuditData call');
            return [];
        }

        isLoadingData.value = true;
        loading.value = true;

        try {
            console.log(`Loading audit data from ${performedTests.value.length} directories:`, performedTests.value);

            // Create an array of promises for each directory
            const loadPromises = performedTests.value.map(async (dir) => {
                if (!dir) return [];

                try {
                    // Fetch the audit.json file
                    const path = `${SCREENSHOT_PATH}/${dir}/audit.json`;
                    console.log(`Fetching audit data from: ${path}`);
                    const auditJson = await fetchJsonFile(path);

                    // If we got an array back, add the datestamp to each item
                    if (Array.isArray(auditJson)) {
                        if (auditJson.length > 0) {
                            console.log(`Found ${auditJson.length} audit items in ${dir}`);
                            return auditJson.map(item => ({
                                ...item,
                                datestamp: dir,
                            }));
                        } else {
                            console.warn(`Empty audit.json in ${dir}`);
                        }
                    } else {
                        console.warn(`Invalid audit.json format in ${dir}:`, auditJson);
                    }

                    // Otherwise return an empty array
                    return [];
                } catch (error) {
                    // Log the error but don't fail the entire operation
                    console.warn(`Error processing ${dir}/audit.json:`, error);
                    return [];
                }
            });

            // Wait for all promises to resolve
            console.log('Waiting for all audit data to load...');
            const results = await Promise.allSettled(loadPromises);

            // Filter for fulfilled promises and flatten the results
            const validResults = results
                .filter((result): result is PromiseFulfilledResult<AuditItem[]> => {
                    if (result.status !== 'fulfilled') {
                        console.warn('Promise rejected:', result);
                        return false;
                    }
                    return true;
                })
                .map(result => result.value);

            const allAuditData = validResults.flat();

            console.log(`Loaded ${allAuditData.length} total audit items from ${validResults.length} directories`);

            return allAuditData;
        } catch (error) {
            console.error('Error loading audit data:', error);
            return [];
        } finally {
            loading.value = false;
            isLoadingData.value = false;
        }
    };

    /**
     * Find a valid test case based on current selections
     */
    const findValidTestCase = () => {
        // Try to find an exact match
        const exactMatch = selectedTestCase.value;

        // If an exact match is found, return it
        if (exactMatch) {
            return;
        }

        // If no exact match is found and we have audit data, try to find a partial match
        if (audit.value && audit.value.length > 0) {
            console.log('No exact match found, looking for partial match');

            // First, try to find a match with the current brand and test
            if (selectedBrand.value && selectedTest.value) {
                const brandTestMatch = audit.value.find(item =>
                    item.testCase.brandSlug === selectedBrand.value &&
                    item.testCase.urlSet.url === selectedTest.value,
                );

                if (brandTestMatch) {
                    console.log('Found match with current brand and test');
                    // Update the other selection values to match this test case
                    selectedViewport.value = brandTestMatch.testCase.viewport.name;
                    selectedBrowser.value = brandTestMatch.testCase.browser.product;
                    selectedDriver.value = brandTestMatch.testCase.driver.name;
                    selectedDomain.value = brandTestMatch.testCase.urlSet.domain;
                    return;
                }
            }

            // If that fails, just pick the first test case for the current brand
            if (selectedBrand.value) {
                const brandMatch = audit.value.find(item =>
                    item.testCase.brandSlug === selectedBrand.value,
                );

                if (brandMatch) {
                    console.log('Found match with current brand');
                    // Update all selection values to match this test case
                    selectedTest.value = brandMatch.testCase.urlSet.url;
                    selectedViewport.value = brandMatch.testCase.viewport.name;
                    selectedBrowser.value = brandMatch.testCase.browser.product;
                    selectedDriver.value = brandMatch.testCase.driver.name;
                    selectedDomain.value = brandMatch.testCase.urlSet.domain;
                    return;
                }
            }

            // If all else fails, just pick the first test case
            const firstCase = audit.value[0];
            console.log('Using first available test case');
            selectedBrand.value = firstCase.testCase.brandSlug;
            selectedTest.value = firstCase.testCase.urlSet.url;
            selectedViewport.value = firstCase.testCase.viewport.name;
            selectedBrowser.value = firstCase.testCase.browser.product;
            selectedDriver.value = firstCase.testCase.driver.name;
            selectedDomain.value = firstCase.testCase.urlSet.domain;
        }
    };

    /**
     * View a specific test case
     */
    const viewTest = (testCase: AuditItem) => {
        selectedBrand.value = testCase.testCase.brandSlug;
        selectedBrowser.value = testCase.testCase.browser.product;
        selectedViewport.value = testCase.testCase.viewport.name;
        selectedDriver.value = testCase.testCase.driver.name;
        selectedTest.value = testCase.testCase.urlSet.url;
        selectedDomain.value = testCase.testCase.urlSet.domain;
        viewType.value = 'individual';

        // Update URL state
        updateUrlState();
    };

    /**
     * Set the view type and update URL
     */
    const setViewType = (type: 'individual' | 'thumbnail' | 'analytics' | 'run-test') => {
        console.log(`Setting view type to: ${type}`);
        viewType.value = type;
        updateUrlState();
    };

    /**
     * Update the URL state based on current selections
     */
    const updateUrlState = () => {
        // Create a new URL object with the current URL
        const url = new URL(window.location.href);

        // Update query parameters
        url.searchParams.set('view', viewType.value);

        if (selectedDate.value) {
            url.searchParams.set('date', selectedDate.value);
        } else {
            url.searchParams.delete('date');
        }

        if (selectedBrand.value) {
            url.searchParams.set('brand', selectedBrand.value);
        } else {
            url.searchParams.delete('brand');
        }

        if (selectedTest.value) {
            url.searchParams.set('test', selectedTest.value);
        } else {
            url.searchParams.delete('test');
        }

        if (selectedViewport.value) {
            url.searchParams.set('viewport', selectedViewport.value);
        } else {
            url.searchParams.delete('viewport');
        }

        if (selectedBrowser.value) {
            url.searchParams.set('browser', selectedBrowser.value);
        } else {
            url.searchParams.delete('browser');
        }

        if (selectedDriver.value) {
            url.searchParams.set('driver', selectedDriver.value);
        } else {
            url.searchParams.delete('driver');
        }

        if (selectedDomain.value) {
            url.searchParams.set('domain', selectedDomain.value);
        } else {
            url.searchParams.delete('domain');
        }

        // Update the URL without reloading the page
        window.history.replaceState({}, '', url.toString());
    };

    /**
     * Add watchers to update URL when filters change
     */
    watch(selectedBrand, () => updateUrlState());
    watch(selectedTest, () => updateUrlState());
    watch(selectedViewport, () => updateUrlState());
    watch(selectedBrowser, () => updateUrlState());
    watch(selectedDriver, () => updateUrlState());
    watch(selectedDomain, () => updateUrlState());
    watch(selectedDate, () => updateUrlState());
    watch(viewType, () => updateUrlState());

    /**
     * Initialize state from URL parameters
     */
    const initFromUrl = () => {
        // Get the current URL parameters
        const params = new URLSearchParams(window.location.search);

        // Set view type from URL
        const urlView = params.get('view');
        if (urlView && ['individual', 'thumbnail', 'analytics', 'run-test'].includes(urlView)) {
            viewType.value = urlView as 'individual' | 'thumbnail' | 'analytics' | 'run-test';
        }

        // Set date from URL
        const urlDate = params.get('date');
        if (urlDate && performedTests.value.includes(urlDate)) {
            selectedDate.value = urlDate;
            fetchConfig(urlDate);
        }

        // Set other parameters from URL
        const urlBrand = params.get('brand');
        if (urlBrand) {
            selectedBrand.value = urlBrand;
        }

        const urlTest = params.get('test');
        if (urlTest) {
            selectedTest.value = urlTest;
        }

        const urlViewport = params.get('viewport');
        if (urlViewport) {
            selectedViewport.value = urlViewport;
        }

        const urlBrowser = params.get('browser');
        if (urlBrowser) {
            selectedBrowser.value = urlBrowser;
        }

        const urlDriver = params.get('driver');
        if (urlDriver) {
            selectedDriver.value = urlDriver;
        }

        const urlDomain = params.get('domain');
        if (urlDomain) {
            selectedDomain.value = urlDomain;
        }
    };

    /**
     * Reset state to default values
     */
    const resetState = () => {
        config.value = {};
        audit.value = [];
        selectedBrand.value = null;
        selectedTest.value = null;
        selectedViewport.value = null;
        selectedBrowser.value = null;
        selectedDriver.value = null;
        selectedDomain.value = null;
    };

    return {
        // State
        performedTests,
        config,
        audit,
        loading,

        // Selected values
        selectedDate,
        selectedBrand,
        selectedTest,
        selectedViewport,
        selectedBrowser,
        selectedDriver,
        selectedDomain,

        // UI state
        viewType,
        diffVisible,

        // Derived state
        brands,
        pages,
        viewports,
        browsers,
        drivers,
        domains,
        selectedTestCase,

        // Actions
        fetchDirectories,
        fetchConfig,
        loadAllAuditData,
        findValidTestCase,
        viewTest,
        setViewType,
        updateUrlState,
        initFromUrl,
        resetState,
    };
});
