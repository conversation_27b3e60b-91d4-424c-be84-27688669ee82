import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { 
  AppConfig, 
  LoadingState, 
  NavigationState, 
  NotificationState, 
  UserPreferences,
  Notification,
  ActionResult 
} from '../types/index.ts';

/**
 * Main application store for global app state
 */
export const useAppStore = defineStore('app', () => {
  // State
  const config = ref<AppConfig>({
    apiBaseUrl: '/api',
    screenshotPath: '/screenshots',
    maxRetries: 3,
    timeout: 30000,
  });

  const loading = ref<LoadingState>({
    isLoading: false,
    error: null,
    lastUpdated: null,
  });

  const navigation = ref<NavigationState>({
    currentRoute: '/',
    breadcrumbs: [],
    sidebarOpen: false,
  });

  const notifications = ref<NotificationState>({
    notifications: [],
    maxNotifications: 5,
  });

  const userPreferences = ref<UserPreferences>({
    theme: 'auto',
    language: 'en',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    defaultView: 'thumbnail',
    autoRefresh: false,
    refreshInterval: 30000,
  });

  // Computed
  const isLoading = computed(() => loading.value.isLoading);
  const hasError = computed(() => !!loading.value.error);
  const currentError = computed(() => loading.value.error);
  const hasNotifications = computed(() => notifications.value.notifications.length > 0);
  const unreadNotifications = computed(() => 
    notifications.value.notifications.filter(n => !n.id.includes('read'))
  );

  // Actions
  const setLoading = (isLoading: boolean, error: string | null = null): void => {
    loading.value.isLoading = isLoading;
    loading.value.error = error;
    loading.value.lastUpdated = new Date();
  };

  const clearError = (): void => {
    loading.value.error = null;
  };

  const updateConfig = (newConfig: Partial<AppConfig>): ActionResult => {
    try {
      config.value = { ...config.value, ...newConfig };
      return {
        success: true,
        data: config.value,
        timestamp: new Date(),
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update config';
      return {
        success: false,
        error: errorMessage,
        timestamp: new Date(),
      };
    }
  };

  const updateUserPreferences = (newPreferences: Partial<UserPreferences>): ActionResult => {
    try {
      userPreferences.value = { ...userPreferences.value, ...newPreferences };
      
      // Persist to localStorage
      localStorage.setItem('oracle-user-preferences', JSON.stringify(userPreferences.value));
      
      return {
        success: true,
        data: userPreferences.value,
        timestamp: new Date(),
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update preferences';
      return {
        success: false,
        error: errorMessage,
        timestamp: new Date(),
      };
    }
  };

  const loadUserPreferences = (): ActionResult => {
    try {
      const stored = localStorage.getItem('oracle-user-preferences');
      if (stored) {
        const parsed = JSON.parse(stored);
        userPreferences.value = { ...userPreferences.value, ...parsed };
      }
      
      return {
        success: true,
        data: userPreferences.value,
        timestamp: new Date(),
      };
    } catch (error) {
      console.warn('Failed to load user preferences from localStorage:', error);
      return {
        success: false,
        error: 'Failed to load user preferences',
        timestamp: new Date(),
      };
    }
  };

  const addNotification = (notification: Omit<Notification, 'id'>): string => {
    const id = `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newNotification: Notification = {
      ...notification,
      id,
    };

    notifications.value.notifications.unshift(newNotification);

    // Remove oldest notifications if we exceed the max
    if (notifications.value.notifications.length > notifications.value.maxNotifications) {
      notifications.value.notifications = notifications.value.notifications.slice(
        0, 
        notifications.value.maxNotifications
      );
    }

    // Auto-remove notification after duration
    if (notification.duration && notification.duration > 0) {
      setTimeout(() => {
        removeNotification(id);
      }, notification.duration);
    }

    return id;
  };

  const removeNotification = (id: string): void => {
    const index = notifications.value.notifications.findIndex(n => n.id === id);
    if (index > -1) {
      notifications.value.notifications.splice(index, 1);
    }
  };

  const clearAllNotifications = (): void => {
    notifications.value.notifications = [];
  };

  const updateNavigation = (route: string, breadcrumbs: NavigationState['breadcrumbs'] = []): void => {
    navigation.value.currentRoute = route;
    navigation.value.breadcrumbs = breadcrumbs;
  };

  const toggleSidebar = (): void => {
    navigation.value.sidebarOpen = !navigation.value.sidebarOpen;
  };

  const setSidebarOpen = (open: boolean): void => {
    navigation.value.sidebarOpen = open;
  };

  // Utility actions
  const showSuccessNotification = (title: string, message: string): string => {
    return addNotification({
      type: 'success',
      title,
      message,
      duration: 5000,
    });
  };

  const showErrorNotification = (title: string, message: string): string => {
    return addNotification({
      type: 'error',
      title,
      message,
      duration: 10000,
    });
  };

  const showWarningNotification = (title: string, message: string): string => {
    return addNotification({
      type: 'warning',
      title,
      message,
      duration: 7000,
    });
  };

  const showInfoNotification = (title: string, message: string): string => {
    return addNotification({
      type: 'info',
      title,
      message,
      duration: 5000,
    });
  };

  // Initialize store
  const initialize = async (): Promise<ActionResult> => {
    try {
      setLoading(true);
      
      // Load user preferences
      loadUserPreferences();
      
      // Apply theme
      document.documentElement.setAttribute('data-theme', userPreferences.value.theme);
      
      setLoading(false);
      
      return {
        success: true,
        timestamp: new Date(),
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to initialize app';
      setLoading(false, errorMessage);
      
      return {
        success: false,
        error: errorMessage,
        timestamp: new Date(),
      };
    }
  };

  return {
    // State
    config: readonly(config),
    loading: readonly(loading),
    navigation: readonly(navigation),
    notifications: readonly(notifications),
    userPreferences: readonly(userPreferences),

    // Computed
    isLoading,
    hasError,
    currentError,
    hasNotifications,
    unreadNotifications,

    // Actions
    setLoading,
    clearError,
    updateConfig,
    updateUserPreferences,
    loadUserPreferences,
    addNotification,
    removeNotification,
    clearAllNotifications,
    updateNavigation,
    toggleSidebar,
    setSidebarOpen,
    showSuccessNotification,
    showErrorNotification,
    showWarningNotification,
    showInfoNotification,
    initialize,
  };
});
