import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { 
  TestRunState, 
  LoadingState, 
  ActionResult 
} from '../types/index.ts';
import { useAppStore } from './appStore.ts';

interface TestRunConfig {
  brands: string[];
  drivers: string[];
  viewports: string[];
  browsers: string[];
  urls: string[];
  username?: string;
  environments?: string[];
  maxConcurrency?: number;
}

interface TestRunResult {
  success: boolean;
  date: string;
  totalTestCases: number;
  executionTime: number;
  estimatedTime?: number;
  error?: string;
}

/**
 * Test run store for managing test execution
 */
export const useTestRunStore = defineStore('testRun', () => {
  const appStore = useAppStore();

  // State
  const state = ref<TestRunState>({
    isRunning: false,
    progress: 0,
    currentStep: '',
    startTime: null,
    estimatedTime: null,
    logs: [],
  });

  const loading = ref<LoadingState>({
    isLoading: false,
    error: null,
    lastUpdated: null,
  });

  const config = ref<TestRunConfig>({
    brands: [],
    drivers: ['puppeteer'],
    viewports: ['desktop'],
    browsers: ['chrome'],
    urls: ['/'],
    username: '',
    environments: ['local', 'production'],
    maxConcurrency: 25,
  });

  const results = ref<TestRunResult[]>([]);
  const currentResult = ref<TestRunResult | null>(null);

  // Computed
  const isLoading = computed(() => loading.value.isLoading);
  const hasError = computed(() => !!loading.value.error);
  const currentError = computed(() => loading.value.error);
  const isRunning = computed(() => state.value.isRunning);
  const hasResults = computed(() => results.value.length > 0);
  const lastResult = computed(() => results.value[results.value.length - 1] || null);

  const elapsedTime = computed(() => {
    if (!state.value.startTime) return 0;
    return Date.now() - state.value.startTime.getTime();
  });

  const remainingTime = computed(() => {
    if (!state.value.estimatedTime || !state.value.startTime) return null;
    const elapsed = elapsedTime.value;
    const estimated = state.value.estimatedTime;
    return Math.max(0, estimated - elapsed);
  });

  const progressPercentage = computed(() => {
    if (!state.value.estimatedTime || !state.value.startTime) return 0;
    const elapsed = elapsedTime.value;
    const estimated = state.value.estimatedTime;
    return Math.min(100, (elapsed / estimated) * 100);
  });

  // Actions
  const setLoading = (isLoading: boolean, error: string | null = null): void => {
    loading.value.isLoading = isLoading;
    loading.value.error = error;
    loading.value.lastUpdated = new Date();
  };

  const clearError = (): void => {
    loading.value.error = null;
  };

  const updateConfig = (newConfig: Partial<TestRunConfig>): void => {
    if (state.value.isRunning) {
      throw new Error('Cannot update config while test is running');
    }
    config.value = { ...config.value, ...newConfig };
  };

  const addLog = (message: string): void => {
    const timestamp = new Date().toISOString();
    state.value.logs.push(`[${timestamp}] ${message}`);
    
    // Keep only last 100 log entries
    if (state.value.logs.length > 100) {
      state.value.logs = state.value.logs.slice(-100);
    }
  };

  const clearLogs = (): void => {
    state.value.logs = [];
  };

  const updateProgress = (progress: number, currentStep: string): void => {
    state.value.progress = Math.max(0, Math.min(100, progress));
    state.value.currentStep = currentStep;
    addLog(`Progress: ${progress}% - ${currentStep}`);
  };

  const validateConfig = (): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!config.value.brands || config.value.brands.length === 0) {
      errors.push('At least one brand must be selected');
    }

    if (!config.value.drivers || config.value.drivers.length === 0) {
      errors.push('At least one driver must be selected');
    }

    if (!config.value.viewports || config.value.viewports.length === 0) {
      errors.push('At least one viewport must be selected');
    }

    if (!config.value.browsers || config.value.browsers.length === 0) {
      errors.push('At least one browser must be selected');
    }

    if (!config.value.urls || config.value.urls.length === 0) {
      errors.push('At least one URL must be selected');
    }

    if (!config.value.username || config.value.username.trim() === '') {
      errors.push('Username is required');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  };

  const estimateExecutionTime = (): number => {
    const totalCombinations = 
      config.value.brands.length *
      config.value.urls.length *
      config.value.browsers.length *
      config.value.viewports.length *
      config.value.drivers.length *
      (config.value.environments?.length || 2);

    // Rough estimate: 5 seconds per test case combination
    return totalCombinations * 5000; // milliseconds
  };

  const runTest = async (): Promise<ActionResult<TestRunResult>> => {
    if (state.value.isRunning) {
      return {
        success: false,
        error: 'Test is already running',
        timestamp: new Date(),
      };
    }

    // Validate configuration
    const validation = validateConfig();
    if (!validation.valid) {
      return {
        success: false,
        error: `Configuration validation failed: ${validation.errors.join(', ')}`,
        timestamp: new Date(),
      };
    }

    try {
      // Initialize test run state
      state.value.isRunning = true;
      state.value.progress = 0;
      state.value.currentStep = 'Initializing test run...';
      state.value.startTime = new Date();
      state.value.estimatedTime = estimateExecutionTime();
      clearLogs();

      addLog('Starting test run...');
      addLog(`Configuration: ${JSON.stringify(config.value, null, 2)}`);

      setLoading(true);

      // Make API request to start test
      const response = await fetch(`${appStore.config.apiBaseUrl}/run-test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config.value),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Test execution failed');
      }

      // Create result object
      const testResult: TestRunResult = {
        success: true,
        date: result.data.date,
        totalTestCases: result.data.totalTestCases,
        executionTime: result.data.executionTime,
        estimatedTime: state.value.estimatedTime,
      };

      currentResult.value = testResult;
      results.value.push(testResult);

      addLog(`Test completed successfully in ${testResult.executionTime}ms`);
      addLog(`Total test cases: ${testResult.totalTestCases}`);

      // Update progress to 100%
      updateProgress(100, 'Test completed successfully');

      setLoading(false);

      // Show success notification
      appStore.showSuccessNotification(
        'Test Completed',
        `Test run completed successfully with ${testResult.totalTestCases} test cases`
      );

      return {
        success: true,
        data: testResult,
        timestamp: new Date(),
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      addLog(`Test failed: ${errorMessage}`);
      
      const failedResult: TestRunResult = {
        success: false,
        date: new Date().toISOString(),
        totalTestCases: 0,
        executionTime: elapsedTime.value,
        error: errorMessage,
      };

      currentResult.value = failedResult;
      results.value.push(failedResult);

      setLoading(false, errorMessage);

      // Show error notification
      appStore.showErrorNotification(
        'Test Failed',
        errorMessage
      );

      return {
        success: false,
        error: errorMessage,
        timestamp: new Date(),
      };
    } finally {
      state.value.isRunning = false;
    }
  };

  const stopTest = async (): Promise<ActionResult> => {
    if (!state.value.isRunning) {
      return {
        success: false,
        error: 'No test is currently running',
        timestamp: new Date(),
      };
    }

    try {
      addLog('Stopping test run...');
      
      // In a real implementation, you would make an API call to stop the test
      // For now, we'll just update the state
      
      state.value.isRunning = false;
      state.value.currentStep = 'Test stopped by user';
      
      addLog('Test run stopped by user');

      appStore.showWarningNotification(
        'Test Stopped',
        'Test run was stopped by user'
      );

      return {
        success: true,
        timestamp: new Date(),
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to stop test';
      
      return {
        success: false,
        error: errorMessage,
        timestamp: new Date(),
      };
    }
  };

  const clearResults = (): void => {
    results.value = [];
    currentResult.value = null;
  };

  const resetState = (): void => {
    state.value = {
      isRunning: false,
      progress: 0,
      currentStep: '',
      startTime: null,
      estimatedTime: null,
      logs: [],
    };
    currentResult.value = null;
    clearError();
  };

  return {
    // State
    state: readonly(state),
    loading: readonly(loading),
    config: readonly(config),
    results: readonly(results),
    currentResult: readonly(currentResult),

    // Computed
    isLoading,
    hasError,
    currentError,
    isRunning,
    hasResults,
    lastResult,
    elapsedTime,
    remainingTime,
    progressPercentage,

    // Actions
    setLoading,
    clearError,
    updateConfig,
    addLog,
    clearLogs,
    updateProgress,
    validateConfig,
    estimateExecutionTime,
    runTest,
    stopTest,
    clearResults,
    resetState,
  };
});
