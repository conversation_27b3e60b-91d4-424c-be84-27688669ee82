import { defineStore } from 'pinia';
import { ref, computed, readonly } from 'vue';
import type { 
  ScreenshotFilters, 
  ViewState, 
  LoadingState, 
  PaginationState,
  ActionResult 
} from '../types/index.ts';
import type { AuditItem } from '@/shared/types/Screenshot/AnalyticsTypes.ts';
import { useAppStore } from './appStore.ts';

/**
 * Screenshot store for managing screenshot test data and UI state
 */
export const useScreenshotStore = defineStore('screenshots', () => {
  const appStore = useAppStore();

  // State
  const performedTests = ref<string[]>([]);
  const config = ref<Record<string, any> | null>(null);
  const audit = ref<AuditItem[]>([]);
  
  const loading = ref<LoadingState>({
    isLoading: false,
    error: null,
    lastUpdated: null,
  });

  const filters = ref<ScreenshotFilters>({
    date: null,
    brand: null,
    test: null,
    viewport: null,
    browser: null,
    driver: null,
    domain: null,
  });

  const view = ref<ViewState>({
    type: 'thumbnail',
    diffVisible: false,
    selectedItem: null,
  });

  const pagination = ref<PaginationState>({
    page: 1,
    pageSize: 20,
    total: 0,
  });

  // Computed
  const isLoading = computed(() => loading.value.isLoading);
  const hasError = computed(() => !!loading.value.error);
  const currentError = computed(() => loading.value.error);
  const hasData = computed(() => audit.value.length > 0);
  const isEmpty = computed(() => !isLoading.value && !hasData.value);

  // Filtered and paginated data
  const filteredAudit = computed(() => {
    let filtered = audit.value;

    // Apply filters
    if (filters.value.brand) {
      filtered = filtered.filter(item => 
        item.testCase.brandSlug === filters.value.brand
      );
    }

    if (filters.value.test) {
      filtered = filtered.filter(item => 
        item.testCase.urlSet.url === filters.value.test
      );
    }

    if (filters.value.viewport) {
      filtered = filtered.filter(item => 
        item.testCase.viewport.name === filters.value.viewport
      );
    }

    if (filters.value.browser) {
      filtered = filtered.filter(item => 
        item.testCase.browser.product === filters.value.browser
      );
    }

    if (filters.value.driver) {
      filtered = filtered.filter(item => 
        item.testCase.driver.name === filters.value.driver
      );
    }

    if (filters.value.domain) {
      filtered = filtered.filter(item => 
        item.testCase.urlSet.domain === filters.value.domain
      );
    }

    return filtered;
  });

  const paginatedAudit = computed(() => {
    const start = (pagination.value.page - 1) * pagination.value.pageSize;
    const end = start + pagination.value.pageSize;
    return filteredAudit.value.slice(start, end);
  });

  // Derived data for filters
  const availableBrands = computed(() => {
    const brands = new Set(audit.value.map(item => item.testCase.brandSlug));
    return Array.from(brands).sort();
  });

  const availableTests = computed(() => {
    const tests = new Set(audit.value.map(item => item.testCase.urlSet.url));
    return Array.from(tests).sort();
  });

  const availableViewports = computed(() => {
    const viewports = new Set(audit.value.map(item => item.testCase.viewport.name));
    return Array.from(viewports).sort();
  });

  const availableBrowsers = computed(() => {
    const browsers = new Set(audit.value.map(item => item.testCase.browser.product));
    return Array.from(browsers).sort();
  });

  const availableDrivers = computed(() => {
    const drivers = new Set(audit.value.map(item => item.testCase.driver.name));
    return Array.from(drivers).sort();
  });

  const availableDomains = computed(() => {
    const domains = new Set(audit.value.map(item => item.testCase.urlSet.domain));
    return Array.from(domains).sort();
  });

  const selectedTestCase = computed(() => {
    if (!view.value.selectedItem) return null;
    return audit.value.find(item => 
      `${item.testCase.brandSlug}-${item.testCase.urlSet.url}-${item.testCase.viewport.name}-${item.testCase.browser.product}-${item.testCase.driver.name}` === view.value.selectedItem
    ) || null;
  });

  // Actions
  const setLoading = (isLoading: boolean, error: string | null = null): void => {
    loading.value.isLoading = isLoading;
    loading.value.error = error;
    loading.value.lastUpdated = new Date();
  };

  const clearError = (): void => {
    loading.value.error = null;
  };

  const updateFilters = (newFilters: Partial<ScreenshotFilters>): void => {
    filters.value = { ...filters.value, ...newFilters };
    
    // Reset pagination when filters change
    pagination.value.page = 1;
    pagination.value.total = filteredAudit.value.length;
  };

  const clearFilters = (): void => {
    filters.value = {
      date: null,
      brand: null,
      test: null,
      viewport: null,
      browser: null,
      driver: null,
      domain: null,
    };
    pagination.value.page = 1;
  };

  const updateView = (newView: Partial<ViewState>): void => {
    view.value = { ...view.value, ...newView };
  };

  const setViewType = (type: ViewState['type']): void => {
    view.value.type = type;
  };

  const toggleDiffVisible = (): void => {
    view.value.diffVisible = !view.value.diffVisible;
  };

  const selectTestCase = (testCase: AuditItem | null): void => {
    if (testCase) {
      const id = `${testCase.testCase.brandSlug}-${testCase.testCase.urlSet.url}-${testCase.testCase.viewport.name}-${testCase.testCase.browser.product}-${testCase.testCase.driver.name}`;
      view.value.selectedItem = id;
    } else {
      view.value.selectedItem = null;
    }
  };

  const updatePagination = (newPagination: Partial<PaginationState>): void => {
    pagination.value = { ...pagination.value, ...newPagination };
  };

  const setPage = (page: number): void => {
    if (page >= 1 && page <= Math.ceil(filteredAudit.value.length / pagination.value.pageSize)) {
      pagination.value.page = page;
    }
  };

  const setPageSize = (pageSize: number): void => {
    pagination.value.pageSize = pageSize;
    pagination.value.page = 1; // Reset to first page
    pagination.value.total = filteredAudit.value.length;
  };

  // Data loading actions
  const fetchDirectories = async (): Promise<ActionResult<string[]>> => {
    try {
      setLoading(true);
      
      const response = await fetch(`${appStore.config.screenshotPath}/directories.json`);
      if (!response.ok) {
        throw new Error(`Failed to fetch directories: ${response.statusText}`);
      }
      
      const directories = await response.json();
      
      if (!Array.isArray(directories)) {
        throw new Error('Invalid directories data format');
      }
      
      performedTests.value = directories.sort().reverse();
      setLoading(false);
      
      return {
        success: true,
        data: directories,
        timestamp: new Date(),
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch directories';
      setLoading(false, errorMessage);
      
      return {
        success: false,
        error: errorMessage,
        timestamp: new Date(),
      };
    }
  };

  const fetchConfig = async (date: string): Promise<ActionResult> => {
    if (!date) {
      return {
        success: false,
        error: 'Date is required',
        timestamp: new Date(),
      };
    }

    try {
      setLoading(true);
      
      const [testResponse, auditResponse] = await Promise.all([
        fetch(`${appStore.config.screenshotPath}/${date}/test.json`),
        fetch(`${appStore.config.screenshotPath}/${date}/audit.json`)
      ]);

      if (!testResponse.ok || !auditResponse.ok) {
        throw new Error('Failed to fetch configuration files');
      }

      const [testConfig, auditData] = await Promise.all([
        testResponse.json(),
        auditResponse.json()
      ]);

      config.value = testConfig || {};
      
      if (Array.isArray(auditData)) {
        audit.value = auditData.map(item => ({
          ...item,
          datestamp: date,
        }));
      } else {
        audit.value = [];
      }

      // Update pagination total
      pagination.value.total = audit.value.length;
      
      setLoading(false);
      
      return {
        success: true,
        data: { config: testConfig, audit: auditData },
        timestamp: new Date(),
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch config';
      setLoading(false, errorMessage);
      
      return {
        success: false,
        error: errorMessage,
        timestamp: new Date(),
      };
    }
  };

  return {
    // State
    performedTests: readonly(performedTests),
    config: readonly(config),
    audit: readonly(audit),
    loading: readonly(loading),
    filters: readonly(filters),
    view: readonly(view),
    pagination: readonly(pagination),

    // Computed
    isLoading,
    hasError,
    currentError,
    hasData,
    isEmpty,
    filteredAudit,
    paginatedAudit,
    availableBrands,
    availableTests,
    availableViewports,
    availableBrowsers,
    availableDrivers,
    availableDomains,
    selectedTestCase,

    // Actions
    setLoading,
    clearError,
    updateFilters,
    clearFilters,
    updateView,
    setViewType,
    toggleDiffVisible,
    selectTestCase,
    updatePagination,
    setPage,
    setPageSize,
    fetchDirectories,
    fetchConfig,
  };
});
