import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { 
  AnalyticsState, 
  LoadingState, 
  ActionResult 
} from '../types/index.ts';
import type { 
  AuditItem, 
  ProcessedChartData, 
  GroupByOption 
} from '@/shared/types/Screenshot/AnalyticsTypes.ts';
import { useScreenshotStore } from './screenshotStore.ts';
import { useAppStore } from './appStore.ts';

/**
 * Analytics store for managing analytics data and visualizations
 */
export const useAnalyticsStore = defineStore('analytics', () => {
  const screenshotStore = useScreenshotStore();
  const appStore = useAppStore();

  // State
  const state = ref<AnalyticsState>({
    groupBy: 'brand',
    showUrlDetails: true,
    selectedEnvironment: 'production',
    dateRange: {
      start: null,
      end: null,
    },
  });

  const loading = ref<LoadingState>({
    isLoading: false,
    error: null,
    lastUpdated: null,
  });

  const processedData = ref<ProcessedChartData[]>([]);
  const rawAnalyticsData = ref<AuditItem[]>([]);

  // Computed
  const isLoading = computed(() => loading.value.isLoading);
  const hasError = computed(() => !!loading.value.error);
  const currentError = computed(() => loading.value.error);
  const hasData = computed(() => processedData.value.length > 0);

  // Get audit data from screenshot store
  const auditData = computed(() => screenshotStore.audit || []);

  // Filtered data based on date range
  const filteredAuditData = computed(() => {
    let filtered = auditData.value;

    if (state.value.dateRange.start) {
      filtered = filtered.filter(item => {
        const itemDate = new Date(item.datestamp || item.testCase.date);
        return itemDate >= state.value.dateRange.start!;
      });
    }

    if (state.value.dateRange.end) {
      filtered = filtered.filter(item => {
        const itemDate = new Date(item.datestamp || item.testCase.date);
        return itemDate <= state.value.dateRange.end!;
      });
    }

    return filtered;
  });

  // Grouped data for charts
  const groupedData = computed(() => {
    const grouped = new Map<string, AuditItem[]>();

    filteredAuditData.value.forEach(item => {
      let groupKey: string;

      switch (state.value.groupBy) {
        case 'brand':
          groupKey = item.testCase.brandSlug;
          break;
        case 'device':
          groupKey = item.testCase.viewport.name;
          break;
        case 'driver':
          groupKey = item.testCase.driver.name;
          break;
        default:
          groupKey = 'unknown';
      }

      if (!grouped.has(groupKey)) {
        grouped.set(groupKey, []);
      }
      grouped.get(groupKey)!.push(item);
    });

    return grouped;
  });

  // Chart data for request counts
  const requestCountData = computed(() => {
    const chartData: ProcessedChartData[] = [];

    groupedData.value.forEach((items, groupKey) => {
      const dataPoints = items.map(item => {
        const date = item.datestamp || item.testCase.date;
        const localRequests = item.screenshotResponse?.local?.requests?.request?.length || 0;
        const productionRequests = item.screenshotResponse?.production?.requests?.request?.length || 0;

        return {
          date,
          local: localRequests,
          production: productionRequests,
          url: item.testCase.urlSet.url,
          brand: item.testCase.brandSlug,
          viewport: item.testCase.viewport.name,
          driver: item.testCase.driver.name,
        };
      });

      chartData.push({
        group: groupKey,
        data: dataPoints,
      });
    });

    return chartData;
  });

  // Load time data
  const loadTimeData = computed(() => {
    const chartData: ProcessedChartData[] = [];

    groupedData.value.forEach((items, groupKey) => {
      const loadTimePoints = items.flatMap(item => {
        const date = item.datestamp || item.testCase.date;
        const points: any[] = [];

        // Local load time
        if (item.loadMetrics?.local?.load) {
          points.push({
            date,
            loadTime: item.loadMetrics.local.load,
            environment: 'local',
            url: item.testCase.urlSet.url,
            brand: item.testCase.brandSlug,
          });
        }

        // Production load time
        if (item.loadMetrics?.production?.load) {
          points.push({
            date,
            loadTime: item.loadMetrics.production.load,
            environment: 'production',
            url: item.testCase.urlSet.url,
            brand: item.testCase.brandSlug,
          });
        }

        return points;
      });

      if (loadTimePoints.length > 0) {
        chartData.push({
          group: groupKey,
          data: [],
          loadTimeData: [{
            url: '',
            brand: groupKey,
            environment: state.value.selectedEnvironment,
            points: loadTimePoints.filter(p => p.environment === state.value.selectedEnvironment),
          }],
        });
      }
    });

    return chartData;
  });

  // Actions
  const setLoading = (isLoading: boolean, error: string | null = null): void => {
    loading.value.isLoading = isLoading;
    loading.value.error = error;
    loading.value.lastUpdated = new Date();
  };

  const clearError = (): void => {
    loading.value.error = null;
  };

  const updateState = (newState: Partial<AnalyticsState>): void => {
    state.value = { ...state.value, ...newState };
  };

  const setGroupBy = (groupBy: GroupByOption): void => {
    state.value.groupBy = groupBy;
    processData();
  };

  const setShowUrlDetails = (show: boolean): void => {
    state.value.showUrlDetails = show;
  };

  const setSelectedEnvironment = (environment: 'local' | 'production'): void => {
    state.value.selectedEnvironment = environment;
  };

  const setDateRange = (start: Date | null, end: Date | null): void => {
    state.value.dateRange.start = start;
    state.value.dateRange.end = end;
    processData();
  };

  const clearDateRange = (): void => {
    state.value.dateRange.start = null;
    state.value.dateRange.end = null;
    processData();
  };

  const processData = async (): Promise<ActionResult> => {
    try {
      setLoading(true);

      // Process the data based on current settings
      const processed = requestCountData.value;
      processedData.value = processed;

      setLoading(false);

      return {
        success: true,
        data: processed,
        timestamp: new Date(),
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to process analytics data';
      setLoading(false, errorMessage);

      return {
        success: false,
        error: errorMessage,
        timestamp: new Date(),
      };
    }
  };

  const loadAllAnalyticsData = async (): Promise<ActionResult> => {
    try {
      setLoading(true);

      // Load data from all available dates
      const directories = screenshotStore.performedTests;
      const allData: AuditItem[] = [];

      for (const date of directories) {
        try {
          const response = await fetch(`${appStore.config.screenshotPath}/${date}/audit.json`);
          if (response.ok) {
            const auditData = await response.json();
            if (Array.isArray(auditData)) {
              allData.push(...auditData.map(item => ({
                ...item,
                datestamp: date,
              })));
            }
          }
        } catch (error) {
          console.warn(`Failed to load analytics data for ${date}:`, error);
        }
      }

      rawAnalyticsData.value = allData;
      await processData();

      setLoading(false);

      return {
        success: true,
        data: allData,
        timestamp: new Date(),
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load analytics data';
      setLoading(false, errorMessage);

      return {
        success: false,
        error: errorMessage,
        timestamp: new Date(),
      };
    }
  };

  const exportData = (format: 'json' | 'csv' = 'json'): ActionResult => {
    try {
      let exportData: string;
      let filename: string;
      let mimeType: string;

      if (format === 'csv') {
        // Convert to CSV
        const headers = ['Date', 'Group', 'Local Requests', 'Production Requests', 'URL', 'Brand'];
        const rows = processedData.value.flatMap(group =>
          group.data.map(point => [
            point.date,
            group.group,
            point.local,
            point.production,
            point.url,
            point.brand,
          ])
        );

        exportData = [headers, ...rows].map(row => row.join(',')).join('\n');
        filename = `oracle-analytics-${new Date().toISOString().split('T')[0]}.csv`;
        mimeType = 'text/csv';
      } else {
        // Export as JSON
        exportData = JSON.stringify(processedData.value, null, 2);
        filename = `oracle-analytics-${new Date().toISOString().split('T')[0]}.json`;
        mimeType = 'application/json';
      }

      // Create download
      const blob = new Blob([exportData], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      return {
        success: true,
        data: { filename, size: exportData.length },
        timestamp: new Date(),
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to export data';
      return {
        success: false,
        error: errorMessage,
        timestamp: new Date(),
      };
    }
  };

  return {
    // State
    state: readonly(state),
    loading: readonly(loading),
    processedData: readonly(processedData),
    rawAnalyticsData: readonly(rawAnalyticsData),

    // Computed
    isLoading,
    hasError,
    currentError,
    hasData,
    auditData,
    filteredAuditData,
    groupedData,
    requestCountData,
    loadTimeData,

    // Actions
    setLoading,
    clearError,
    updateState,
    setGroupBy,
    setShowUrlDetails,
    setSelectedEnvironment,
    setDateRange,
    clearDateRange,
    processData,
    loadAllAnalyticsData,
    exportData,
  };
});
