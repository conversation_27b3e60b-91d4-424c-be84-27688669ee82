// Store type definitions
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
}

export interface FilterState {
  search: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  filters: Record<string, any>;
}

// Screenshot specific types
export interface ScreenshotFilters {
  date?: string;
  brand?: string;
  test?: string;
  viewport?: string;
  browser?: string;
  driver?: string;
  domain?: string;
}

export interface ViewState {
  type: 'individual' | 'thumbnail' | 'analytics' | 'run-test';
  diffVisible: boolean;
  selectedItem: string | null;
}

export interface TestRunState {
  isRunning: boolean;
  progress: number;
  currentStep: string;
  startTime: Date | null;
  estimatedTime: number | null;
  logs: string[];
}

// Analytics specific types
export interface AnalyticsState {
  groupBy: 'brand' | 'device' | 'driver';
  showUrlDetails: boolean;
  selectedEnvironment: 'local' | 'production';
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
}

// Configuration types
export interface AppConfig {
  apiBaseUrl: string;
  screenshotPath: string;
  maxRetries: number;
  timeout: number;
}

// User preferences
export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  timezone: string;
  defaultView: ViewState['type'];
  autoRefresh: boolean;
  refreshInterval: number;
}

// Navigation state
export interface NavigationState {
  currentRoute: string;
  breadcrumbs: Array<{
    label: string;
    path: string;
  }>;
  sidebarOpen: boolean;
}

// Notification types
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
}

export interface NotificationState {
  notifications: Notification[];
  maxNotifications: number;
}

// Root store state
export interface RootState {
  app: {
    config: AppConfig;
    loading: LoadingState;
    navigation: NavigationState;
    notifications: NotificationState;
    userPreferences: UserPreferences;
  };
  screenshots: {
    data: any[]; // Will be properly typed in screenshot store
    filters: ScreenshotFilters;
    view: ViewState;
    loading: LoadingState;
    pagination: PaginationState;
  };
  analytics: {
    state: AnalyticsState;
    data: any[]; // Will be properly typed in analytics store
    loading: LoadingState;
  };
  testRun: {
    state: TestRunState;
    config: any; // Will be properly typed
    results: any[]; // Will be properly typed
  };
}

// Action result types
export interface ActionResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: Date;
}

// Store action types
export type StoreAction<T = any> = (...args: any[]) => Promise<ActionResult<T>>;
export type StoreGetter<T = any> = () => T;
export type StoreMutation<T = any> = (payload: T) => void;
