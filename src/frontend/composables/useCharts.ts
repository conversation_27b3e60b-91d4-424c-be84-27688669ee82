import {onBeforeUnmount, ref} from 'vue';
import Chart from 'chart.js/auto';
import 'chartjs-adapter-date-fns'; // Import the date adapter
import type {ChartGroupData, GroupByOption} from '@/shared/types/Screenshot/AnalyticsTypes.ts';

/**
 * Composable for managing Chart.js instances
 */
export function useCharts(
    chartData: Ref<ChartGroupData[]>,
    groupBy: Ref<GroupByOption>,
) {
    const chartInstances = ref<Chart[]>([]);

    /**
     * Get a color from CSS variables
     */
    const getChartColor = (): string => {
        // Use CSS variables for chart colors
        const colors = [
            getComputedStyle(document.documentElement).getPropertyValue('--chart-color-1').trim(),
            getComputedStyle(document.documentElement).getPropertyValue('--chart-color-2').trim(),
            getComputedStyle(document.documentElement).getPropertyValue('--chart-color-3').trim(),
            getComputedStyle(document.documentElement).getPropertyValue('--chart-color-4').trim(),
            getComputedStyle(document.documentElement).getPropertyValue('--chart-color-5').trim(),
            getComputedStyle(document.documentElement).getPropertyValue('--chart-color-6').trim(),
            getComputedStyle(document.documentElement).getPropertyValue('--chart-color-7').trim(),
            getComputedStyle(document.documentElement).getPropertyValue('--chart-color-8').trim(),
        ];

        // Fallback colors if CSS variables are not available
        const fallbackColors = [
            '#4285F4', // Google Blue
            '#EA4335', // Google Red
            '#FBBC05', // Google Yellow
            '#34A853', // Google Green
            '#FF6D01', // Orange
            '#46BDC6', // Teal
            '#7B1FA2', // Purple
            '#0097A7', // Cyan
        ];

        // Use the first valid color from CSS variables or fallback to predefined colors
        const validColors = colors.filter(color => color && color !== '');
        return validColors.length > 0
            ? validColors[Math.floor(Math.random() * validColors.length)]
            : fallbackColors[Math.floor(Math.random() * fallbackColors.length)];
    };

    /**
     * Parse a date string to a Date object
     */
    const parseDate = (dateStr: string): Date => {
        try {
            // Format: 2025-04-22--12:08
            const [datePart, timePart] = dateStr.split('--');
            const [year, month, day] = datePart.split('-').map(Number);
            const [hour, minute] = timePart.split(':').map(Number);

            // Month is 0-indexed in JavaScript Date
            return new Date(year, month - 1, day, hour, minute);
        } catch (e) {
            console.error('Error parsing date:', dateStr, e);
            return new Date();
        }
    };

    // Debounce timer reference
    let debounceTimer: number | null = null;

    // Flag to prevent multiple chart creations
    let isCreatingCharts = false;

    /**
     * Create charts for the provided data with debouncing
     */
    const createCharts = (): void => {
        // Skip if already creating charts
        if (isCreatingCharts) {
            console.log('Already creating charts, skipping');
            return;
        }

        // Clear any existing debounce timer
        if (debounceTimer !== null) {
            clearTimeout(debounceTimer);
        }

        // Set a new debounce timer
        debounceTimer = window.setTimeout(() => {
            isCreatingCharts = true;
            console.log('Creating charts...');

            // Clear any existing chart instances
            destroyCharts();

            // Create new charts
            chartData.value.forEach((chart, index) => {
                const canvas = document.getElementById(`chart-${index}`) as HTMLCanvasElement | null;
                if (!canvas) return;

                const ctx = canvas.getContext('2d');
                if (!ctx) return;

                // Prepare data for the chart
                // Format dates as strings to avoid date adapter issues
                const labels = chart.data.map(point => {
                    const date = parseDate(point.date);
                    return date.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                    });
                });
                const datasets = [{
                    label: `Total ${groupBy.value === 'brand' ? 'Brand' : groupBy.value === 'device' ? 'Device' : 'Driver'} Requests`,
                    data: chart.data.map(point => point.count),
                    borderColor: getChartColor(),
                    backgroundColor: 'rgba(66, 133, 244, 0.2)',
                    borderWidth: 2,
                    tension: 0.1,
                    fill: true,
                }];

                // Create the chart
                const chartInstance = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels,
                        datasets,
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: true, // Set to true to prevent infinite resizing
                        resizeDelay: 200, // Add delay to prevent resize loops
                        animation: {
                            duration: 0, // Disable animations to prevent resize loops
                        },
                        plugins: {
                            title: {
                                display: false,
                            },
                            tooltip: {
                                callbacks: {
                                    title: (tooltipItems) => {
                                        const date = tooltipItems[0].label;
                                        if (date instanceof Date) {
                                            return date.toLocaleString();
                                        }
                                        return String(date);
                                    },
                                },
                            },
                        },
                        scales: {
                            x: {
                                // Use a simple category scale instead of time scale
                                // to avoid date adapter issues
                                type: 'category',
                                title: {
                                    display: true,
                                    text: 'Date',
                                },
                            },
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Number of Requests',
                                },
                            },
                        },
                    },
                });

                chartInstances.value.push(chartInstance);
            });

            // Reset the flag after charts are created
            isCreatingCharts = false;
            console.log('Charts created successfully');
        }, 300); // 300ms debounce delay
    };

    /**
     * Destroy all chart instances
     */
    const destroyCharts = (): void => {
        chartInstances.value.forEach(chart => chart.destroy());
        chartInstances.value = [];
    };

    // Clean up chart instances when component is unmounted
    onBeforeUnmount(destroyCharts);

    return {
        chartInstances,
        createCharts,
        destroyCharts,
        getChartColor,
        parseDate,
    };
}
