import { ref, onMounted, onUnmounted, computed, readonly } from 'vue';
import { useTestRunStore } from '@/frontend/stores/modules/testRunStore.ts';
import { useAppStore } from '@/frontend/stores/modules/appStore.ts';

interface WebSocketMessage {
  type: 'test-progress' | 'test-log' | 'test-status' | 'test-result';
  data: any;
  timestamp: string;
}

/**
 * Composable for WebSocket connection to backend
 */
export function useWebSocket() {
  const testRunStore = useTestRunStore();
  const appStore = useAppStore();

  const ws = ref<WebSocket | null>(null);
  const isConnected = ref(false);
  const isConnecting = ref(false);
  const reconnectAttempts = ref(0);
  const maxReconnectAttempts = 5;
  const reconnectDelay = 1000; // Start with 1 second

  // Computed
  const connectionStatus = computed(() => {
    if (isConnecting.value) return 'connecting';
    if (isConnected.value) return 'connected';
    return 'disconnected';
  });

  // Connect to WebSocket
  const connect = (): void => {
    if (ws.value?.readyState === WebSocket.OPEN) {
      return; // Already connected
    }

    if (isConnecting.value) {
      return; // Already trying to connect
    }

    try {
      isConnecting.value = true;
      
      // Determine WebSocket URL
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = window.location.host;
      const wsUrl = `${protocol}//${host}/ws`;

      console.log('Connecting to WebSocket:', wsUrl);
      
      ws.value = new WebSocket(wsUrl);

      ws.value.onopen = handleOpen;
      ws.value.onmessage = handleMessage;
      ws.value.onclose = handleClose;
      ws.value.onerror = handleError;
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      isConnecting.value = false;
      scheduleReconnect();
    }
  };

  // Handle WebSocket open
  const handleOpen = (): void => {
    console.log('WebSocket connected');
    isConnected.value = true;
    isConnecting.value = false;
    reconnectAttempts.value = 0;

    // Subscribe to all channels
    send({
      type: 'subscribe',
      channels: ['test-progress', 'test-log', 'test-status', 'test-result'],
    });
  };

  // Handle WebSocket message
  const handleMessage = (event: MessageEvent): void => {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      switch (message.type) {
        case 'test-progress':
          handleTestProgress(message.data);
          break;
          
        case 'test-log':
          handleTestLog(message.data);
          break;
          
        case 'test-status':
          handleTestStatus(message.data);
          break;
          
        case 'test-result':
          handleTestResult(message.data);
          break;
          
        default:
          console.log('Unknown WebSocket message type:', message.type);
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  };

  // Handle WebSocket close
  const handleClose = (event: CloseEvent): void => {
    console.log('WebSocket disconnected:', event.code, event.reason);
    isConnected.value = false;
    isConnecting.value = false;

    // Only reconnect if it wasn't a clean close
    if (event.code !== 1000 && reconnectAttempts.value < maxReconnectAttempts) {
      scheduleReconnect();
    }
  };

  // Handle WebSocket error
  const handleError = (error: Event): void => {
    console.error('WebSocket error:', error);
    isConnecting.value = false;
  };

  // Schedule reconnection
  const scheduleReconnect = (): void => {
    if (reconnectAttempts.value >= maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      appStore.showErrorNotification(
        'Connection Lost',
        'Lost connection to server. Please refresh the page.'
      );
      return;
    }

    const delay = reconnectDelay * Math.pow(2, reconnectAttempts.value); // Exponential backoff
    reconnectAttempts.value++;

    console.log(`Reconnecting in ${delay}ms (attempt ${reconnectAttempts.value}/${maxReconnectAttempts})`);
    
    setTimeout(() => {
      connect();
    }, delay);
  };

  // Send message to WebSocket
  const send = (message: any): void => {
    if (ws.value?.readyState === WebSocket.OPEN) {
      ws.value.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected, cannot send message:', message);
    }
  };

  // Handle test progress updates
  const handleTestProgress = (data: any): void => {
    testRunStore.updateProgress(data.progress, data.currentStep);
  };

  // Handle test log messages
  const handleTestLog = (data: any): void => {
    testRunStore.addLog(data.message);
  };

  // Handle test status updates
  const handleTestStatus = (data: any): void => {
    console.log('Test status update:', data);
    
    // Update test run state based on status
    switch (data.status) {
      case 'starting':
        // Test is starting - this is handled by the test run store
        break;
        
      case 'running':
        // Test is running - update any relevant state
        break;
        
      case 'completed':
        // Test completed successfully
        appStore.showSuccessNotification(
          'Test Completed',
          'Screenshot test completed successfully'
        );
        break;
        
      case 'failed':
        // Test failed
        appStore.showErrorNotification(
          'Test Failed',
          data.error || 'Screenshot test failed'
        );
        break;
        
      case 'stopped':
        // Test was stopped
        appStore.showWarningNotification(
          'Test Stopped',
          'Screenshot test was stopped'
        );
        break;
    }
  };

  // Handle test results
  const handleTestResult = (data: any): void => {
    console.log('Test result received:', data);
    // The test result is handled by the test run store
  };

  // Disconnect WebSocket
  const disconnect = (): void => {
    if (ws.value) {
      ws.value.close(1000, 'Client disconnect');
      ws.value = null;
    }
    isConnected.value = false;
    isConnecting.value = false;
  };

  // Ping server to keep connection alive
  const ping = (): void => {
    send({ type: 'ping' });
  };

  // Setup ping interval
  let pingInterval: number | null = null;

  const startPingInterval = (): void => {
    if (pingInterval) return;
    
    pingInterval = window.setInterval(() => {
      if (isConnected.value) {
        ping();
      }
    }, 30000); // Ping every 30 seconds
  };

  const stopPingInterval = (): void => {
    if (pingInterval) {
      clearInterval(pingInterval);
      pingInterval = null;
    }
  };

  // Lifecycle
  onMounted(() => {
    connect();
    startPingInterval();
  });

  onUnmounted(() => {
    stopPingInterval();
    disconnect();
  });

  return {
    // State
    isConnected: readonly(isConnected),
    isConnecting: readonly(isConnecting),
    connectionStatus,
    reconnectAttempts: readonly(reconnectAttempts),

    // Methods
    connect,
    disconnect,
    send,
    ping,
  };
}
