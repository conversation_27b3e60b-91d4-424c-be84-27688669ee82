import {onMounted, ref, watch} from 'vue';
import {useRoute, useRouter} from 'vue-router';
import type {GroupByOption} from '@/shared/types/Screenshot/AnalyticsTypes.ts';

export type ViewType = 'individual' | 'thumbnail' | 'analytics';

/**
 * Composable for syncing component state with URL query parameters
 */
export function useUrlState() {
    const router = useRouter();
    const route = useRoute();

    // Define reactive state with default values
    const viewType = ref<ViewType>('thumbnail');
    const selectedDate = ref<string | null>(null);
    const selectedBrand = ref<string | null>(null);
    const selectedTest = ref<string | null>(null);
    const selectedViewport = ref<string | null>(null);
    const selectedBrowser = ref<string | null>(null);
    const selectedDriver = ref<string | null>(null);

    // Analytics specific state
    const groupBy = ref<GroupByOption>('brand');
    const endpointFilter = ref<string>('/s/u');
    const showUrlDetails = ref<boolean>(false);

    // Update URL when state changes
    const updateUrl = () => {
        const query: Record<string, string> = {};

        if (viewType.value) query.view = viewType.value;
        if (selectedDate.value) query.date = selectedDate.value;

        // Only include relevant parameters based on view type
        if (viewType.value === 'individual' || viewType.value === 'thumbnail') {
            if (selectedBrand.value) query.brand = selectedBrand.value;

            if (viewType.value === 'individual') {
                if (selectedTest.value) query.test = selectedTest.value;
                if (selectedViewport.value) query.viewport = selectedViewport.value;
                if (selectedBrowser.value) query.browser = selectedBrowser.value;
                if (selectedDriver.value) query.driver = selectedDriver.value;
            }
        } else if (viewType.value === 'analytics') {
            if (groupBy.value) query.groupBy = groupBy.value;
            if (endpointFilter.value) query.filter = endpointFilter.value;
            if (showUrlDetails.value) query.details = 'true';
        }

        // Only update if query params have changed
        if (JSON.stringify(query) !== JSON.stringify(route.query)) {
            router.replace({query});
        }
    };

    // Load state from URL on mount
    const loadFromUrl = () => {
        const {query} = route;

        // Set view type
        if (query.view && ['individual', 'thumbnail', 'analytics'].includes(query.view as string)) {
            viewType.value = query.view as ViewType;
        }

        // Set date
        if (query.date) {
            selectedDate.value = query.date as string;
        }

        // Set other parameters based on view type
        if (viewType.value === 'individual' || viewType.value === 'thumbnail') {
            if (query.brand) selectedBrand.value = query.brand as string;

            if (viewType.value === 'individual') {
                if (query.test) selectedTest.value = query.test as string;
                if (query.viewport) selectedViewport.value = query.viewport as string;
                if (query.browser) selectedBrowser.value = query.browser as string;
                if (query.driver) selectedDriver.value = query.driver as string;
            }
        } else if (viewType.value === 'analytics') {
            if (query.groupBy && ['brand', 'device', 'driver'].includes(query.groupBy as string)) {
                groupBy.value = query.groupBy as GroupByOption;
            }

            if (query.filter) endpointFilter.value = query.filter as string;
            showUrlDetails.value = query.details === 'true';
        }
    };

    // Watch for state changes and update URL
    watch([
        viewType,
        selectedDate,
        selectedBrand,
        selectedTest,
        selectedViewport,
        selectedBrowser,
        selectedDriver,
        groupBy,
        endpointFilter,
        showUrlDetails,
    ], updateUrl, {deep: true});

    // Watch for URL changes and update state
    watch(() => route.query, loadFromUrl, {deep: true});

    // Load state from URL on mount
    onMounted(loadFromUrl);

    return {
        // State
        viewType,
        selectedDate,
        selectedBrand,
        selectedTest,
        selectedViewport,
        selectedBrowser,
        selectedDriver,
        groupBy,
        endpointFilter,
        showUrlDetails,

        // Methods
        updateUrl,
        loadFromUrl,
    };
}
