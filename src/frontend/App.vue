<template>
  <div id="app" :data-theme="currentTheme">
    <!-- Global loading overlay -->
    <AppLoading
      v-if="isInitializing"
      type="spinner"
      size="lg"
      message="Initializing Oracle..."
      description="Loading application data and preferences"
      overlay
    />

    <!-- Main application layout -->
    <div v-else class="app-layout">
      <!-- Header -->
      <AppHeader />

      <!-- Main content -->
      <main class="app-main">
        <router-view v-slot="{ Component, route }">
          <Transition name="page" mode="out-in">
            <component :is="Component" :key="route.path" />
          </Transition>
        </router-view>
      </main>

      <!-- Global error boundary -->
      <div v-if="hasGlobalError" class="global-error">
        <div class="error-content">
          <h2>Something went wrong</h2>
          <p>{{ globalErrorMessage }}</p>
          <AppButton @click="retryInitialization">
            Retry
          </AppButton>
        </div>
      </div>
    </div>

    <!-- Global notifications -->
    <AppNotifications />
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed, ref } from 'vue';
import { useAppStore } from '@/frontend/stores/modules/appStore.ts';
import { useStoreState } from '@/frontend/stores/composables/useStoreState.ts';
import { useStoreActions } from '@/frontend/stores/composables/useStoreActions.ts';
import AppHeader from '@/frontend/components/Common/AppHeader.vue';
import AppLoading from '@/frontend/components/ui/AppLoading.vue';
import AppButton from '@/frontend/components/ui/AppButton.vue';
import AppNotifications from '@/frontend/components/ui/AppNotifications.vue';

const appStore = useAppStore();
const { hasError, currentErrors } = useStoreState();
const { refreshAllData } = useStoreActions();

const isInitializing = ref(true);

// Computed properties
const currentTheme = computed(() => appStore.userPreferences.theme);
const hasGlobalError = computed(() => hasError.value && isInitializing.value);
const globalErrorMessage = computed(() => currentErrors.value.join('; '));

// Initialize application
const initializeApp = async (): Promise<void> => {
  try {
    isInitializing.value = true;

    // Initialize app store (loads preferences, etc.)
    await appStore.initialize();

    // Load initial data
    await refreshAllData();

    isInitializing.value = false;
  } catch (error) {
    console.error('Failed to initialize app:', error);
    isInitializing.value = false;
  }
};

const retryInitialization = async (): Promise<void> => {
  appStore.clearError();
  await initializeApp();
};

// Lifecycle
onMounted(() => {
  initializeApp();
});
</script>

<style lang="scss">
@use "@/frontend/style/variables";
@use "@/frontend/style/defaults";

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.global-error {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .error-content {
    background: var(--color-background, #ffffff);
    padding: 2rem;
    border-radius: 0.5rem;
    text-align: center;
    max-width: 400px;
    margin: 1rem;

    h2 {
      margin: 0 0 1rem 0;
      color: var(--color-danger, #ef4444);
    }

    p {
      margin: 0 0 1.5rem 0;
      color: var(--color-text-secondary, #6b7280);
    }
  }
}

// Page transitions
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
</style>
