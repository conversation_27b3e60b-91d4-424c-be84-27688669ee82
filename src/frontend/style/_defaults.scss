html, body {
    margin: 0;
    padding: 0;
    background-color: var(--color-background-primary);
    color: var(--color-text-primary);
    transition: background-color var(--transition-normal), color var(--transition-normal);
    line-height: 1.5;
    font-size: 16px;
}

#app {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    padding: var(--spacing-md);
    margin: 0 auto;
}

*, *::before, *::after {
    box-sizing: border-box;
}

a {
    color: var(--color-primary);
    text-decoration: none;
    transition: all var(--transition-fast);
    position: relative;
    font-weight: var(--font-weight-medium);

    &:hover {
        color: var(--color-primary-dark);
    }

    &:focus {
        outline: none;
        text-decoration: underline;
    }
}

button {
    padding: var(--spacing-sm) var(--spacing-md);
    margin-top: var(--spacing-xs);
    border: 1px solid transparent;
    border-radius: var(--border-radius-md);
    background-color: var(--color-background-secondary);
    color: var(--color-text-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-fast);

    &:hover:not(:disabled) {
        background-color: var(--color-background-tertiary);
    }

    &:focus {
        outline: none;
        box-shadow: 0 0 0 2px var(--color-primary-light);
    }

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    &.primary {
        background-color: var(--color-primary);
        color: var(--color-text-inverse);

        &:hover:not(:disabled) {
            background-color: var(--color-primary-dark);
        }
    }

    &.secondary {
        background-color: transparent;
        border: 1px solid var(--color-border-primary);

        &:hover:not(:disabled) {
            border-color: var(--color-primary);
            color: var(--color-primary);
        }
    }
}

/* Common utility classes */
.card {
    background-color: var(--color-background-secondary);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
    border: 1px solid var(--color-border-secondary);

    &:hover {
        box-shadow: var(--shadow-md);
    }
}

/* Typography utility classes */
.text-primary {
    color: var(--color-text-primary);
}

.text-secondary {
    color: var(--color-text-secondary);
}

.text-tertiary {
    color: var(--color-text-tertiary);
}

.text-xs {
    font-size: var(--font-size-xs);
}

.text-sm {
    font-size: var(--font-size-sm);
}

.text-md {
    font-size: var(--font-size-md);
}

.text-lg {
    font-size: var(--font-size-lg);
}

.text-xl {
    font-size: var(--font-size-xl);
}

.text-2xl {
    font-size: var(--font-size-2xl);
}

.text-3xl {
    font-size: var(--font-size-3xl);
}

.font-light {
    font-weight: var(--font-weight-light);
}

.font-normal {
    font-weight: var(--font-weight-normal);
}

.font-medium {
    font-weight: var(--font-weight-medium);
}

.font-semibold {
    font-weight: var(--font-weight-semibold);
}

.font-bold {
    font-weight: var(--font-weight-bold);
}

/* Background utility classes */
.bg-primary {
    background-color: var(--color-background-primary);
}

.bg-secondary {
    background-color: var(--color-background-secondary);
}

.bg-tertiary {
    background-color: var(--color-background-tertiary);
}

/* Form elements - minimalistic */
input, select, textarea {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--color-border-primary);
    border-radius: var(--border-radius-md);
    background-color: var(--color-background-primary);
    color: var(--color-text-primary);
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);

    &:focus {
        outline: none;
        border-color: var(--color-primary);
        box-shadow: 0 0 0 2px var(--color-primary-light);
    }

    &:disabled {
        background-color: var(--color-background-disabled);
        color: var(--color-text-disabled);
        cursor: not-allowed;
    }

    &::placeholder {
        color: var(--color-text-tertiary);
        opacity: 0.7;
    }
}

/* Modern, minimalistic tables */
table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin: var(--spacing-md) 0;
    font-size: var(--font-size-sm);

    th, td {
        padding: var(--spacing-md);
        text-align: left;
        border-bottom: 1px solid var(--color-border-secondary);
    }

    th {
        font-weight: var(--font-weight-semibold);
        color: var(--color-text-secondary);
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-size: var(--font-size-xs);
        background-color: var(--color-background-secondary);
    }

    tr:hover {
        background-color: var(--color-background-secondary);
    }

    tbody tr:last-child td {
        border-bottom: none;
    }
}

/* Modern layout utilities */
.container {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.gap-sm {
    gap: var(--spacing-sm);
}

.gap-md {
    gap: var(--spacing-md);
}

.gap-lg {
    gap: var(--spacing-lg);
}

/* Spacing utilities */
.m-0 {
    margin: 0;
}

.mt-sm {
    margin-top: var(--spacing-sm);
}

.mt-md {
    margin-top: var(--spacing-md);
}

.mt-lg {
    margin-top: var(--spacing-lg);
}

.mb-sm {
    margin-bottom: var(--spacing-sm);
}

.mb-md {
    margin-bottom: var(--spacing-md);
}

.mb-lg {
    margin-bottom: var(--spacing-lg);
}

.p-0 {
    padding: 0;
}

.p-sm {
    padding: var(--spacing-sm);
}

.p-md {
    padding: var(--spacing-md);
}

.p-lg {
    padding: var(--spacing-lg);
}
