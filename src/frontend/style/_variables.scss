html {
    /* Base colors - more minimal, modern palette */
    --color-primary: #2563eb; /* Modern blue */
    --color-primary-dark: #1d4ed8;
    --color-primary-light: #60a5fa;
    --color-secondary: #10b981; /* Modern green */
    --color-secondary-dark: #059669;
    --color-accent: #f43f5e; /* Modern pink/red */
    --color-accent-dark: #e11d48;
    --color-warning: #f59e0b; /* Modern amber */
    --color-warning-dark: #d97706;

    /* Text colors - higher contrast for minimalism */
    --color-text-primary: #111827;
    --color-text-secondary: #4b5563;
    --color-text-tertiary: #6b7280;
    --color-text-disabled: #9ca3af;
    --color-text-inverse: #f9fafb;

    /* Background colors - cleaner, more minimal */
    --color-background-primary: #ffffff;
    --color-background-secondary: #f9fafb;
    --color-background-tertiary: #f3f4f6;
    --color-background-disabled: #e5e7eb;
    --color-background-overlay: rgba(17, 24, 39, 0.5);

    /* Border colors - subtle */
    --color-border-primary: #e5e7eb;
    --color-border-secondary: #f3f4f6;
    --color-border-focus: #60a5fa;

    /* Status colors */
    --color-success: #10b981;
    --color-error: #f43f5e;
    --color-info: #2563eb;

    /* Chart colors - modern, vibrant but not overwhelming */
    --chart-color-1: #2563eb; /* Blue */
    --chart-color-2: #f43f5e; /* Pink */
    --chart-color-3: #f59e0b; /* Amber */
    --chart-color-4: #10b981; /* Green */
    --chart-color-5: #8b5cf6; /* Purple */
    --chart-color-6: #06b6d4; /* Cyan */
    --chart-color-7: #ec4899; /* Pink */
    --chart-color-8: #14b8a6; /* Teal */

    /* Shadows - more subtle for minimalism */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.08), 0 2px 4px -1px rgba(0, 0, 0, 0.04);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.04);

    /* Spacing - more generous for minimalism */
    --spacing-xs: 0.375rem;
    --spacing-sm: 0.75rem;
    --spacing-md: 1.25rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-xxl: 4rem;

    /* Border radius - more modern, slightly rounded */
    --border-radius-sm: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-full: 9999px;

    /* Font sizes - modern scale */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-md: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* Font weights - modern */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Transitions - smooth */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    /* Legacy variables - keep for backward compatibility */
    --app_background-color: var(--color-background-primary);
    --text_color: var(--color-text-primary);
    --card_background-color: var(--color-background-secondary);

    &.theme--dark {
        /* Text colors - modern dark mode */
        --color-text-primary: #f9fafb;
        --color-text-secondary: #e5e7eb;
        --color-text-tertiary: #d1d5db;
        --color-text-disabled: #9ca3af;
        --color-text-inverse: #111827;

        /* Background colors - modern, subtle dark mode */
        --color-background-primary: #111827;
        --color-background-secondary: #1f2937;
        --color-background-tertiary: #374151;
        --color-background-disabled: #4b5563;
        --color-background-overlay: rgba(249, 250, 251, 0.1);

        /* Border colors - subtle */
        --color-border-primary: #374151;
        --color-border-secondary: #4b5563;
        --color-border-focus: #60a5fa;

        /* Primary colors - adjusted for dark mode */
        --color-primary-light: #2563eb;
        --color-primary: #3b82f6;
        --color-primary-dark: #60a5fa;

        /* Secondary colors - adjusted for dark mode */
        --color-secondary: #10b981;
        --color-secondary-dark: #34d399;

        /* Accent colors - adjusted for dark mode */
        --color-accent: #f43f5e;
        --color-accent-dark: #fb7185;

        /* Shadows - more subtle for dark mode */
        --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);

        /* Legacy variables - keep for backward compatibility */
        --app_background-color: var(--color-background-primary);
        --text_color: var(--color-text-primary);
        --card_background-color: var(--color-background-secondary);
    }
}
