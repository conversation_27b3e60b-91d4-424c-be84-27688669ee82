<template>
    <div v-if="!testCase" class="no-test-case">
        <p>No test case selected or available. Please select a valid combination of brand, page, viewport, browser, and
            driver.</p>
    </div>
    <ul v-else :class="mappedClassModifiers" class="info-list">
        <li class="info-item">
            <strong>Brand:</strong> {{ testCase.testCase.brandSlug }}
        </li>
        <li class="info-item">
            <strong>Domain:</strong> {{ testCase.testCase.urlSet.domain }}
        </li>
        <li class="info-item">
            <strong>Page:</strong> {{ testCase.testCase.urlSet.url }}
        </li>
        <li class="info-item">
            <strong>Viewport:</strong> {{ testCase.testCase.viewport.name }}
        </li>
        <li class="info-item">
            <strong>Browser:</strong> {{ testCase.testCase.browser.displayName }}
        </li>
        <li class="info-item">
            <strong>Driver:</strong> {{ testCase.testCase.driver.name }}
        </li>
        <li class="info-item">
            <strong>Diff:</strong> {{ testCase.diffAmount }}px
        </li>
        <li class="info-item">
            <strong>Links: </strong>
            <span>
                <a :href="testCase.testCase.urlSet.local" target="_blank">Local</a> |
                <a :href="testCase.testCase.urlSet.production" target="_blank">Production</a>
            </span>
        </li>
    </ul>
</template>

<script lang="ts" setup>
import {computed} from 'vue';

const props = defineProps({
    testCase: {
        required: false,
        type: Object,
        default: null,
    },
    modifiers: {
        required: false,
        type: Array,
        default: () => [],
    },
});

const mappedClassModifiers = computed(() => props.modifiers.map(modifier => `info-list--${modifier}`));
</script>

<style lang="scss" scoped>
.no-test-case {
    padding: var(--spacing-md);
    margin: 2rem auto;
    max-width: 30rem;
    background-color: var(--color-background-secondary);
    border-radius: var(--border-radius-md);
    text-align: center;

    p {
        color: var(--color-text-secondary);
        font-size: var(--font-size-md);
        line-height: 1.5;
    }
}

.info-list {
    list-style-type: none;
    padding: var(--spacing-md);
    font-family: var(--font-family, Arial, sans-serif);
    margin: 2rem auto;
    border: 1px solid var(--color-border-secondary);
    border-radius: var(--border-radius-md);
    max-width: 30rem;
    background-color: var(--color-background-secondary);

    &--full-width {
        width: 100%;
        max-width: unset;
    }

    .info-item {
        margin-bottom: var(--spacing-sm);
        font-size: var(--font-size-sm);
        display: flex;
        justify-content: space-between;
        padding: var(--spacing-xs) 0;
        border-bottom: 1px solid var(--color-border-secondary);

        &:last-of-type {
            margin-bottom: 0;
            border-bottom: none;
        }

        strong {
            font-weight: var(--font-weight-semibold);
            color: var(--color-text-secondary);
        }

        a {
            text-decoration: none;
            color: var(--color-primary);
            transition: color var(--transition-fast);

            &:hover {
                color: var(--color-primary-dark);
                text-decoration: underline;
            }
        }
    }
}
</style>
