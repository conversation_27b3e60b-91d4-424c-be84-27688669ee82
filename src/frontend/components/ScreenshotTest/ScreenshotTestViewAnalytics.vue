<template>
    <div class="analytics-container">
        <div v-if="analyticsStore.loading" class="loading-indicator">
            Loading analytics data...
        </div>
        <div v-else>
            <div class="filter-controls">
                <div class="filter-group">
                    <label for="group-by-select">Group By:</label>
                    <select id="group-by-select" v-model="analyticsStore.groupBy">
                        <option value="brand">Brand</option>
                        <option value="device">Device</option>
                        <option value="driver">Driver</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="environment-select">Environment:</label>
                    <select id="environment-select" v-model="analyticsStore.selectedEnvironment">
                        <option value="local">Local</option>
                        <option value="production">Production</option>
                    </select>
                </div>
                <div class="filter-group">
                    <button
                        aria-label="Share current view"
                        class="share-button"
                        @click="analyticsStore.shareUrl"
                    >
                        Share View
                    </button>
                </div>
            </div>

            <div class="charts-container">
                <analytics-charts
                    v-model:show-url-details="analyticsStore.showUrlDetails"
                    :data="analyticsStore.processedData"
                    :group-by="analyticsStore.groupBy"
                    :loading="analyticsStore.loading"
                />
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import {onMounted, watch} from 'vue';
import AnalyticsCharts from '@/frontend/components/ScreenshotTest/AnalyticsCharts.vue';
import {useAnalyticsStore} from '@/frontend/stores/analyticsStore';
import {useScreenshotStore} from '@/frontend/stores/screenshotStore';

// Use the stores
const analyticsStore = useAnalyticsStore();
const screenshotStore = useScreenshotStore();

// Flag to prevent multiple data loads
let dataLoaded = false;

// Watch for changes in the view type
watch(() => screenshotStore.viewType, (newValue) => {
    if (newValue === 'analytics' && !dataLoaded) {
        console.log('View changed to analytics, loading data...');
        loadAnalyticsData();
    }
});

// Function to load analytics data
function loadAnalyticsData() {
    console.log('Loading analytics data...');

    // Initialize from URL parameters
    const params = new URLSearchParams(window.location.search);
    analyticsStore.initFromUrl(params);

    // Load data
    analyticsStore.loadData().then(() => {
        console.log('Analytics data loaded, processed data:',
            analyticsStore.processedData.length, 'groups');

        if (analyticsStore.processedData.length === 0) {
            console.log('No processed data available, check console for errors');
        }
    });

    dataLoaded = true;
}

// Load data on component mount, but only once
onMounted(() => {
    if (!dataLoaded && screenshotStore.viewType === 'analytics') {
        console.log('ScreenshotTestViewAnalytics mounted, loading data...');
        loadAnalyticsData();
    } else {
        console.log('Data already loaded or not in analytics view, skipping loadData call');
    }
});
</script>

<style lang="scss" scoped>
.analytics-container {
    padding: 0;
    transition: all var(--transition-normal);

    .loading-indicator {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 300px;
        font-size: var(--font-size-lg);
        color: var(--color-text-secondary);
        background-color: var(--color-background-secondary);
        border-radius: var(--border-radius-lg);
        transition: all var(--transition-normal);
        position: relative;

        &::before {
            content: '';
            position: absolute;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid var(--color-border-secondary);
            border-top-color: var(--color-primary);
            animation: spin 1s linear infinite;
            margin-bottom: var(--spacing-lg);
        }

        &::after {
            content: 'Loading analytics data...';
            margin-top: 60px;
            font-weight: var(--font-weight-medium);
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
    }

    .filter-controls {
        display: flex;
        flex-wrap: wrap;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-xl);
        padding: var(--spacing-lg);
        background-color: var(--color-background-secondary);
        border-radius: var(--border-radius-lg);
        transition: all var(--transition-normal);
        align-items: flex-end;

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);

            label {
                font-weight: var(--font-weight-medium);
                color: var(--color-text-secondary);
                font-size: var(--font-size-sm);
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }

            select, input {
                padding: var(--spacing-sm) var(--spacing-md);
                border: none;
                border-radius: var(--border-radius-md);
                background-color: var(--color-background-primary);
                color: var(--color-text-primary);
                transition: all var(--transition-fast);
                min-width: 180px;
                box-shadow: var(--shadow-sm);

                &:focus {
                    outline: none;
                    box-shadow: 0 0 0 2px var(--color-primary-light);
                }

                &::placeholder {
                    color: var(--color-text-tertiary);
                    opacity: 0.7;
                }
            }

            .share-button {
                padding: var(--spacing-sm) var(--spacing-md);
                background-color: var(--color-primary);
                color: var(--color-text-inverse);
                border: none;
                border-radius: var(--border-radius-md);
                font-weight: var(--font-weight-medium);
                cursor: pointer;
                transition: all var(--transition-fast);
                min-width: 120px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                height: 38px; /* Match height of inputs */

                &:hover {
                    background-color: var(--color-primary-dark);
                    transform: translateY(-1px);
                }

                &:focus {
                    outline: none;
                    box-shadow: 0 0 0 2px var(--color-primary-light);
                }

                &:active {
                    transform: translateY(1px);
                }
            }
        }
    }

    .charts-container {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xl);
    }
}

@media (max-width: 768px) {
    .analytics-container {
        .filter-controls {
            flex-direction: column;
            align-items: stretch;
            padding: var(--spacing-md);

            .filter-group {
                width: 100%;

                select, input, .share-button {
                    width: 100%;
                    min-width: 0;
                }
            }
        }
    }
}
</style>
