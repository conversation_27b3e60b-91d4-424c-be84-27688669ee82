<template>
    <div class="image-comparison-container">
        <img-comparison-slider v-if="productionImage && localImage" class="image-comparison-slider">
            <img slot="first" :src="productionImage" style="width: 100%"/>
            <img slot="second" :src="localImage" style="width: 100%"/>
        </img-comparison-slider>

        <img v-if="screenshotStore.diffVisible" :src="diffImage" alt="" class="diff-image">
    </div>
</template>

<script setup>
import {ImgComparisonSlider} from '@img-comparison-slider/vue';
import {defineProps, ref, watch} from "vue";
import {useScreenshotStore} from '@/frontend/stores/screenshotStore';

const props = defineProps({
    testCase: {
        required: false,
        type: Object,
        default: null
    },
});

// Use the screenshot store
const screenshotStore = useScreenshotStore();

const productionImage = ref(null);
const localImage = ref(null);
const diffImage = ref(null);

watch(() => props.testCase, (newTestCase) => {
    if (newTestCase && newTestCase.filePath) {
        productionImage.value = `${newTestCase.filePath}/production.png`.replace(import.meta.env.VITE_PUBLIC_PATH, '');
        localImage.value = `${newTestCase.filePath}/local.png`.replace(import.meta.env.VITE_PUBLIC_PATH, '');
        diffImage.value = `${newTestCase.filePath}/diff.png`.replace(import.meta.env.VITE_PUBLIC_PATH, '');
    } else {
        productionImage.value = null;
        localImage.value = null;
        diffImage.value = null;
    }
}, {
    deep: true,
    immediate: true,
});
</script>

<style lang="scss" scoped>
.image-comparison-container {
    display: flex;
    position: relative;

    img {
        user-select: none;
    }

    .image-comparison-slider {
        margin-inline: auto;

        --divider-color: rgba(0, 0, 0, 0.5);
        --default-handle-color: rgba(0, 0, 0, 0.5);
    }

    .diff-image {
        inset: 0;
        pointer-events: none;
        margin-inline: auto;
        position: absolute;
        max-width: 100%;
    }
}
</style>
