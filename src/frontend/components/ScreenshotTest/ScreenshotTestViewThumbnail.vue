<template>
    <div>
        <div class="grid-controls">
            <app-select id="columns" :options="columnsRange" :value="columnsPerRow" label="Items per row"
                        @change="columnsPerRow = $event"/>

            <button @click="displayInformation = !displayInformation">
                Display test data
            </button>

            <app-select id="group-by" :options="groupByOptions" :value="groupByKey" label="Group by"
                        @change="groupByKey = $event"/>

            <button @click="screenshotStore.viewType = 'analytics'">
                View Analytics
            </button>
        </div>

        <template v-if="groupByKey">
            <div v-for="(auditItems, key) in groupedAuditItems" :key="key">
                <h3>{{ key }}</h3>
                <screenshot-test-view-thumbnail-group :audit-items="auditItems" :columns="columnsPerRow"
                                                      :display-information="displayInformation"/>
            </div>
        </template>
        <screenshot-test-view-thumbnail-group v-else :audit-items="sortedAuditItems"
                                              :columns="columnsPerRow" :display-information="displayInformation"/>
    </div>
</template>

<script lang="ts" setup>
import useLocalstorageRef from '@/frontend/customRefs/localstorageRef.js';
import {groupBy, range, sortBy} from 'lodash-es';
import AppSelect from '@/frontend/components/Common/AppSelect.vue';
import {computed} from 'vue';
import ScreenshotTestViewThumbnailGroup
    from '@/frontend/components/ScreenshotTest/ScreenshotTestViewThumbnailGroup.vue';
import {useScreenshotStore} from '@/frontend/stores/screenshotStore';

// Use the screenshot store
const screenshotStore = useScreenshotStore();

const groupByOptions = [
    'brand',
    'domain',
    'page',
    'driver',
    'viewport',
    'browser',
];

const columnsRange = range(1, 11);
const columnsPerRow = useLocalstorageRef('oracle--thumbnail-column-count', 4);
const displayInformation = useLocalstorageRef('oracle--thumbnail-display-info', false);
const groupByKey = useLocalstorageRef('oracle--thumbnail-group-by', null);

const sortedAuditItems = computed(() => sortBy(screenshotStore.audit, ['diffAmount']).reverse());
const groupedAuditItems = computed(() => {
    if (groupByKey.value === null) {
        return null;
    }

    return groupBy(sortedAuditItems.value, (auditItem) => {
        if (groupByKey.value === 'brand') {
            return auditItem.testCase.brandSlug;
        } else if (groupByKey.value === 'browser') {
            return auditItem.testCase.browser.displayName;
        } else if (groupByKey.value === 'driver') {
            return auditItem.testCase.driver.name;
        } else if (groupByKey.value === 'viewport') {
            return auditItem.testCase.viewport.name;
        } else if (groupByKey.value === 'page') {
            return auditItem.testCase.urlSet.url;
        } else if (groupByKey.value === 'domain') {
            return auditItem.testCase.urlSet.domain;
        }

        console.error(`Unsupported grouping option ${groupByKey.value}`);

        return null;
    });
});
</script>

<style lang="scss" scoped>
.grid-controls {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;

    button {
        height: max-content;
        margin-top: auto;
    }
}
</style>
