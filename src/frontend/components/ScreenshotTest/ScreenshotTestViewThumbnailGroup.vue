<template>
    <div
        :style="{ gridTemplateColumns: `repeat(${columns}, 1fr)` }"
        class="screenshot-test-thumbnails"
    >
        <div
            v-for="(testCase, index) in auditItems"
            :key="index"
            class="screenshot-test-thumbnails--thumbnail"
        >
            <img
                :alt="getImageAlt(testCase)"
                :src="getImagePath(testCase.testCase.filePath)"
                class="screenshot-test-thumbnails--thumbnail-image"
                @click="screenshotStore.viewTest(testCase)"
            />

            <screenshot-test-info v-if="displayInformation" :modifiers="['full-width']" :test-case="testCase"/>
        </div>
    </div>
</template>

<script lang="ts" setup>
import ScreenshotTestInfo from '@/frontend/components/ScreenshotTest/ScreenshotTestInfo.vue';
import {useScreenshotStore} from '@/frontend/stores/screenshotStore';

defineProps({
    auditItems: {
        type: Array,
        required: true,
    },
    columns: {
        type: Number,
        required: true,
    },
    displayInformation: {
        type: <PERSON><PERSON><PERSON>,
    },
});

// Use the screenshot store
const screenshotStore = useScreenshotStore();

const getImagePath = (filePath) => `${filePath}/diff.png`.replace(import.meta.env.VITE_PUBLIC_PATH, '');
const getImageAlt = (testCase) => `Screenshot for ${testCase.testCase.brandSlug} - ${testCase.testCase.viewport.name}`;
</script>

<style lang="scss" scoped>
.screenshot-test-thumbnails {
    display: grid;
    grid-gap: 1.5rem;
    transition: grid-template-columns 0.3s ease;

    &--thumbnail {
        display: flex;
        flex-direction: column;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;

        &:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        }

        &-image {
            max-width: 100%;
            object-fit: contain;
            border-radius: 8px;
            cursor: pointer;
            max-height: 50vh;
        }
    }
}
</style>
