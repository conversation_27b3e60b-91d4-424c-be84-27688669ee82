<template>
    <div class="charts-wrapper">
        <div v-if="loading" class="loading-message">
            Processing chart data...
        </div>
        <div v-else-if="!chartData.length" class="no-data-message">
            <div class="message-content">
                <h3>No matching data found</h3>
                <p>This could be due to:</p>
                <ul>
                    <li>No audit.json files exist in the screenshots directories</li>
                    <li>The audit data is in an unexpected format</li>
                    <li>No test runs have been completed yet</li>
                </ul>
                <p>Try the following:</p>
                <ul>
                    <li><strong>Run a screenshot test</strong> using the "Create New Test" button</li>
                    <li>Try a different grouping option (Brand, Device, or Driver)</li>
                    <li>Check the browser console for more detailed error information</li>
                    <li>Verify that screenshot tests have been run and generated audit.json files</li>
                </ul>
            </div>
        </div>
        <div v-else>
            <!-- Load Time Charts -->
            <div class="chart-section">
                <h2 class="section-title">Page Load Times</h2>
                <div v-for="(chart, index) in chartData" :key="`load-${index}`" class="chart-container">
                    <h3 class="chart-title">{{ chart.group }} <span class="subtitle">({{ groupBy }})</span></h3>
                    <div v-if="chart.loadTimeData && chart.loadTimeData.length > 0">
                        <canvas :id="`load-time-chart-${index}`" class="chart-canvas"></canvas>
                    </div>
                    <div v-else class="no-data-message">
                        <p>No load time data available for this group</p>
                    </div>
                </div>
            </div>

            <!-- Request Diff Charts -->
            <div class="chart-section">
                <h2 class="section-title">Request Changes Over Time</h2>
                <div v-for="(chart, index) in chartData" :key="`diff-${index}`" class="chart-container">
                    <h3 class="chart-title">{{ chart.group }} <span class="subtitle">({{ groupBy }})</span></h3>
                    <div v-if="chart.requestDiffData && chart.requestDiffData.length > 0">
                        <canvas :id="`request-diff-chart-${index}`" class="chart-canvas"></canvas>
                    </div>
                    <div v-else class="no-data-message">
                        <p>No request diff data available for this group</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import {computed, onBeforeUnmount, onMounted, ref, watch} from 'vue';
import Chart from 'chart.js/auto';
import {useCharts} from '@/frontend/composables/useCharts';

// Props
const props = defineProps({
    data: {
        type: Array,
        required: true,
    },
    groupBy: {
        type: String,
        required: true,
    },
    loading: {
        type: Boolean,
        default: false,
    },
});

// Emits
const emit = defineEmits(['update:showUrlDetails']);

// State
const chartData = ref([]);
const charts = ref([]);

// Computed
const showUrlDetails = computed({
    get: () => props.showUrlDetails,
    set: (value) => emit('update:showUrlDetails', value),
});

// Use charts composable for date parsing
const {parseDate} = useCharts(chartData, computed(() => props.groupBy));

/**
 * Process data for chart display
 */
const processChartData = () => {
    if (!props.data || props.data.length === 0) {
        console.log('No data available for charts');
        chartData.value = [];
        return;
    }

    console.log(`Processing chart data for ${props.data.length} groups`);

    // Check if any group has data
    const hasData = props.data.some(group => group.data && group.data.length > 0);
    if (!hasData) {
        console.log('No data points found in any group');
        chartData.value = [];
        return;
    }

    // Log the first group's data to help debug
    if (props.data[0] && props.data[0].data) {
        console.log('First group data:', JSON.stringify(props.data[0].data, null, 2));
    }

    // Set chart data
    chartData.value = props.data;
};

/**
 * Create charts for the processed data
 */
const createCharts = () => {
    // Destroy existing charts
    destroyCharts();

    console.log('Creating charts...');

    // Create load time charts
    chartData.value.forEach((chart, index) => {
        if (!chart.loadTimeData || chart.loadTimeData.length === 0) {
            return;
        }

        const ctx = document.getElementById(`load-time-chart-${index}`);
        if (!ctx) {
            console.warn(`Canvas element not found for load time chart ${index}`);
            return;
        }

        // Prepare data for Chart.js
        const datasets = chart.loadTimeData.map(series => {
            // Generate a color based on the URL and brand
            const hue = Math.abs(series.url.split('').reduce((a, b) => a + b.charCodeAt(0), 0) % 360);
            const saturation = 70 + (Math.abs(series.brand.charCodeAt(0)) % 30);
            const lightness = 50;

            return {
                label: `${series.brand} - ${series.url} (${series.environment})`,
                data: series.points.map(point => ({
                    x: point.date,
                    y: point.loadTime,
                })),
                borderColor: `hsl(${hue}, ${saturation}%, ${lightness}%)`,
                backgroundColor: `hsla(${hue}, ${saturation}%, ${lightness}%, 0.1)`,
                tension: 0.4,
                fill: false,
            };
        });

        // Create the chart
        const loadTimeChart = new Chart(ctx, {
            type: 'line',
            data: {
                datasets,
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'category',
                        title: {
                            display: true,
                            text: 'Date',
                        },
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Load Time (ms)',
                        },
                        beginAtZero: true,
                    },
                },
                plugins: {
                    title: {
                        display: true,
                        text: `Load Times for ${chart.group}`,
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                const label = context.dataset.label || '';
                                const value = context.parsed.y || 0;
                                return `${label}: ${value.toFixed(0)}ms`;
                            },
                        },
                    },
                },
            },
        });

        charts.value.push(loadTimeChart);
    });

    // Create request diff charts
    chartData.value.forEach((chart, index) => {
        if (!chart.requestDiffData || chart.requestDiffData.length === 0) {
            return;
        }

        const ctx = document.getElementById(`request-diff-chart-${index}`);
        if (!ctx) {
            console.warn(`Canvas element not found for request diff chart ${index}`);
            return;
        }

        // Prepare data for Chart.js
        // We'll create a stacked bar chart with added, removed, and changed requests
        const labels = [...new Set(chart.requestDiffData.flatMap(series =>
            series.points.map(point => point.date),
        ))].sort();

        const datasets = chart.requestDiffData.map(series => {
            // Generate a color based on the URL
            const hue = Math.abs(series.url.split('').reduce((a, b) => a + b.charCodeAt(0), 0) % 360);

            // Create datasets for added, removed, and changed
            return [
                {
                    label: `${series.url} - Added`,
                    data: labels.map(date => {
                        const point = series.points.find(p => p.date === date);
                        return point ? point.added : 0;
                    }),
                    backgroundColor: `hsla(${hue}, 70%, 50%, 0.7)`,
                    stack: series.url,
                },
                {
                    label: `${series.url} - Removed`,
                    data: labels.map(date => {
                        const point = series.points.find(p => p.date === date);
                        return point ? -point.removed : 0; // Negative for removed
                    }),
                    backgroundColor: `hsla(${hue}, 70%, 30%, 0.7)`,
                    stack: series.url,
                },
                {
                    label: `${series.url} - Changed`,
                    data: labels.map(date => {
                        const point = series.points.find(p => p.date === date);
                        return point ? point.changed : 0;
                    }),
                    backgroundColor: `hsla(${hue}, 70%, 70%, 0.7)`,
                    stack: series.url,
                },
            ];
        }).flat();

        // Create the chart
        const requestDiffChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels,
                datasets,
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Date',
                        },
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Request Changes',
                        },
                        stacked: true,
                    },
                },
                plugins: {
                    title: {
                        display: true,
                        text: `Request Changes for ${chart.group}`,
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                const label = context.dataset.label || '';
                                const value = Math.abs(context.parsed.y || 0);
                                return `${label}: ${value}`;
                            },
                        },
                    },
                },
            },
        });

        charts.value.push(requestDiffChart);
    });

    console.log('Charts created successfully');
};

/**
 * Destroy all charts
 */
const destroyCharts = () => {
    charts.value.forEach(chart => {
        if (chart) {
            chart.destroy();
        }
    });
    charts.value = [];
};

// Watch for changes in props
watch(() => props.data, () => {
    processChartData();
    createCharts();
}, {deep: true});

watch(() => props.groupBy, () => {
    processChartData();
    createCharts();
});

// Initialize on mount
onMounted(() => {
    console.log('Initializing charts...');
    processChartData();
    createCharts();
});

// Clean up on unmount
onBeforeUnmount(() => {
    destroyCharts();
});
</script>

<style lang="scss" scoped>
.charts-wrapper {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    padding: var(--spacing-md);

    .chart-section {
        margin-bottom: var(--spacing-xl);

        .section-title {
            font-size: var(--font-size-xl);
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-sm);
            border-bottom: 1px solid var(--color-border-secondary);
        }
    }

    .loading-message, .no-data-message {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
        font-size: var(--font-size-lg);
        color: var(--color-text-secondary);
        background-color: var(--color-background-secondary);
        border-radius: var(--border-radius-lg);
        padding: var(--spacing-lg);
        text-align: center;

        .message-content {
            max-width: 600px;

            h3 {
                margin-top: 0;
                margin-bottom: var(--spacing-md);
                color: var(--color-text-primary);
            }

            ul {
                text-align: left;
                margin-bottom: var(--spacing-md);

                li {
                    margin-bottom: var(--spacing-sm);
                }
            }
        }
    }

    .chart-container {
        background-color: var(--color-background-secondary);
        border-radius: var(--border-radius-lg);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
        box-shadow: var(--shadow-sm);
        transition: all var(--transition-normal);

        &:hover {
            box-shadow: var(--shadow-md);
        }

        .chart-title {
            font-size: var(--font-size-xl);
            color: var(--color-text-primary);
            margin-top: 0;
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;

            .subtitle {
                font-size: var(--font-size-sm);
                color: var(--color-text-secondary);
                margin-left: var(--spacing-sm);
                font-weight: var(--font-weight-normal);
            }
        }

        .chart-canvas {
            height: 300px;
            width: 100%;
        }
    }
}

@media (max-width: 768px) {
    .charts-wrapper {
        .chart-container {
            padding: var(--spacing-md);

            .chart-title {
                font-size: var(--font-size-lg);
                flex-direction: column;
                align-items: flex-start;

                .subtitle {
                    margin-left: 0;
                    margin-top: var(--spacing-xs);
                }
            }

            .chart-canvas {
                height: 250px;
            }
        }
    }
}
</style>
