<template>
    <h5>{{ title }}:</h5>

    <div class="requests-grid">
        <div class="requests-grid--header">
            <div class="requests-grid--header-item">URL</div>
            <div class="requests-grid--header-item">Times Requested</div>
            <div class="requests-grid--header-item">Status</div>
            <div class="requests-grid--header-item">Initiator Details</div>
            <div class="requests-grid--header-item">Comparisons (Local vs Production)</div>
        </div>
        <div v-for="(urlData, url) in getUrlCounts(requests)" :key="url" class="requests-grid--data">
            <div :class="{ 'mismatch': urlData.countMismatch }" class="requests-grid--data-item">
                {{ url }}
            </div>
            <div :class="{ 'mismatch': urlData.countMismatch }" class="requests-grid--data-item">
                {{ urlData.count }} requests
            </div>
            <div :class="{ 'error': isErrorStatus(urlData.status) }" class="requests-grid--data-item">
                <span v-if="urlData.status" :title="getStatusText(urlData.status)">
                    {{ urlData.status }}
                    <span v-if="isErrorStatus(urlData.status)" class="error-icon">⚠️</span>
                </span>
                <span v-else>-</span>
            </div>
            <div class="requests-grid--data-item">
                <ul v-if="urlData.initiators">
                    <template v-for="(initiator, index) in urlData.initiators">
                        <li><strong>Type:</strong> {{ initiator.type }}</li>
                        <li v-for="(frame, index) in initiator.stack?.callFrames" :key="index">
                            <strong>Function Name:</strong> {{ frame.functionName || 'Anonymous' }}<br>
                            <strong>Line Number:</strong> {{ frame.lineNumber }}<br>
                            <strong>Column Number:</strong> {{ frame.columnNumber }}<br>
                        </li>
                        <hr v-if="index < urlData.initiators.length - 1">
                    </template>
                </ul>
                <template v-else>
                    -
                </template>
            </div>
            <div class="requests-grid--data-item">
                <div v-if="isComparisonNeeded(url)">
                    <strong>Local:</strong>
                    <span :class="{ 'mismatch': isMismatch(url, 'local') }">
                        {{ url }}
                    </span>
                    <strong>Production:</strong>
                    <span :class="{ 'mismatch': isMismatch(url, 'production') }">
                        {{ url }}
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
const props = defineProps({
    title: {
        type: String,
        required: true,
    },
    requests: {
        type: Array,
        required: false,
    },
    isResponse: {
        type: Boolean,
        required: true,
    },
    brandSlug: {
        type: String,
        required: true,
    },
});

interface Timing {
    start: number;
    end: number;
}

interface ScreenshotRequest {
    url: string;
    timing: Timing;
    status?: number;
}

const getUrlCounts = (requests: ScreenshotRequest[]) => {
    const urlCounts: Record<string, {
        count: number;
        countMismatch?: boolean;
        initiator?: any;
        status?: number;
        timings?: Timing[];
        initiators?: any[];
    }> = {};

    requests?.forEach((request) => {
        const url = cleanUrl(request.url);

        if (urlCounts[url]) {
            urlCounts[url].count++;
            if (request.timing) {
                urlCounts[url].timings.push(request.timing);
            }
            if (request.initiator) {
                urlCounts[url].initiators.push(request.initiator);
            }
            // Keep track of error status codes with priority
            if (request.status && (!urlCounts[url].status || isErrorStatus(request.status))) {
                urlCounts[url].status = request.status;
            }
        } else {
            urlCounts[url] = {
                count: 1,
                timings: request.timing ? [request.timing] : [],
                initiators: request.initiator ? [request.initiator] : [],
                status: request.status,
                ...request,
            };
        }
    });

    return urlCounts;
};

const cleanUrl = (url: string) => {
    const regex = new RegExp(`(?:https?://[^/]+)?([^?]+)(?:\\?.*)?$`, 'g');
    return url.includes(props.brandSlug) ? url.replace(regex, '$1') : url;
};

const isComparisonNeeded = (url: string) => {
    return url.includes(props.brandSlug);
};

const isMismatch = (url: string, environment: 'local' | 'production') => {
    const cleanedUrl = cleanUrl(url);
    const envRequests = props.requests.find(req => req[environment]);
    const matchedUrl = envRequests?.find((req: any) => cleanUrl(req.url) === cleanedUrl);
    return matchedUrl ? matchedUrl.timing.start !== matchedUrl.timing.end : false;
};

const isErrorStatus = (status: number | undefined) => {
    if (!status) return false;
    return status < 200 || status >= 400;
};

const getStatusText = (status: number) => {
    const statusTexts = {
        200: 'OK',
        201: 'Created',
        204: 'No Content',
        301: 'Moved Permanently',
        302: 'Found',
        304: 'Not Modified',
        400: 'Bad Request',
        401: 'Unauthorized',
        403: 'Forbidden',
        404: 'Not Found',
        500: 'Internal Server Error',
        502: 'Bad Gateway',
        503: 'Service Unavailable',
        504: 'Gateway Timeout',
    };

    return statusTexts[status] || `Status Code ${status}`;
};
</script>

<style lang="scss" scoped>
.requests-grid {
    .requests-grid--header,
    .requests-grid--data {
        display: grid;
        grid-template-columns: 4fr 1fr 1fr 2fr 1fr;
    }

    .requests-grid--header,
    .requests-grid--data {
        &-item {
            border: 1px solid var(--text_color);
            padding: 0.25rem 0.5rem;
            word-wrap: break-word;
            line-break: anywhere;

            li {
                line-break: strict;
            }

            &.error {
                background-color: rgba(255, 0, 0, 0.1);
                color: #d32f2f;
                font-weight: bold;
            }

            .error-icon {
                margin-left: 5px;
            }
        }
    }
}
</style>
