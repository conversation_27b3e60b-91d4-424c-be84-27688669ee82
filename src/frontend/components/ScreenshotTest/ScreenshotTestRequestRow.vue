<template>
    <tr>
        <td>{{ testCase.testCase.brandSlug }}</td>
        <td>{{ localRequestCount }}</td>
        <td>{{ productionRequestCount }}</td>
        <td :style="{ fontWeight: diffAmount !== 0 ? 'bold' : 'normal' }">{{ diffAmount }}</td>
        <td>
            <StatusIndicator :passing="diffAmount === 0"/>
        </td>
        <td>
            <button @click="viewTest(testCase)">View test</button>
        </td>
    </tr>
    <tr>
        <td colspan="6">
            <table>
                <thead>
                <tr>
                    <th>Url</th>
                    <th>Local</th>
                    <th>Production</th>
                    <th>Diff production - local</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="(request, url) in requests" :key="url">
                    <td>{{ url }}</td>
                    <td>{{ request.local }}</td>
                    <td>{{ request.production }}</td>
                    <td>{{ request.production - request.local }}</td>
                </tr>
                </tbody>
            </table>
        </td>
    </tr>
</template>

<script lang="ts" setup>
import {computed, defineProps, inject, PropType} from 'vue';
import StatusIndicator from '@/frontend/components/Common/StatusIndicator.vue';

const props = defineProps({
    testCase: {
        type: Object as PropType<{
            testCase: any;
            diffAmount: number;
            passing: boolean;
        }>,
        required: true,
    },
    dataKey: {
        type: String,
        required: true,
    },
});

const viewTest = inject('viewTest');

const localRequestCount = computed(() => props.testCase.testCase.screenshotResponse?.local.requests[props.dataKey]?.length || 0);
const productionRequestCount = computed(() => props.testCase.testCase.screenshotResponse?.production.requests[props.dataKey]?.length || 0);
const diffAmount = computed(() => localRequestCount.value - productionRequestCount.value);

const cleanUrl = (url: string) => {
    const regex = new RegExp(`(?:https?://[^/]+)?([^?]+)(?:\\?.*)?$`, 'g');
    return url.includes(props.testCase.testCase.brandSlug) ? url.replace(regex, '$1') : url;
};

const requests = computed(() => {
    const requests = {};

    props.testCase.testCase.screenshotResponse?.local.requests[props.dataKey]?.forEach(request => {
        console.log(request);
        const cleanedUrl = cleanUrl(request.url);
        requests[cleanedUrl] ||= {};
        requests[cleanedUrl].local ||= 0;
        requests[cleanedUrl].production ||= 0;
        requests[cleanedUrl].local++;
    });
    props.testCase.testCase.screenshotResponse?.production.requests[props.dataKey]?.forEach(request => {
        console.log(request);
        const cleanedUrl = cleanUrl(request.url);
        requests[cleanedUrl] ||= {};
        requests[cleanedUrl].local ||= 0;
        requests[cleanedUrl].production ||= 0;
        requests[cleanedUrl].production++;
    });

    return requests;
});
</script>

<style lang="scss" scoped>
table {
    width: 100%;
    text-align: left;
    margin-block: 1rem;
    line-break: anywhere;

    th, td {
        min-width: 6rem;
    }
}
</style>
