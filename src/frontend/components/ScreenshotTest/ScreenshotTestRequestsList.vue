<template>
    <div v-if="screenshotResponse">
        <template v-for="(environment, envKey) in screenshotResponse" :key="envKey">
            <div v-if="environment.requests.response || environment.requests.request">
                <h3>{{ envKey }} Environment</h3>

                <screenshot-test-requests-list-inner
                    v-if="environment.requests.response"
                    :brand-name="testCase.testCase.brandSlug"
                    :is-response="true"
                    :requests="environment.requests.response"
                    title="Response URLs"/>

                <screenshot-test-requests-list-inner
                    v-if="environment.requests.request"
                    :brand-name="testCase.testCase.brandSlug"
                    :is-response="false"
                    :requests="environment.requests.request"
                    title="Request URLs"/>
            </div>
        </template>
    </div>
</template>

<script lang="ts" setup>
import ScreenshotTestRequestsListInner from '@/frontend/components/ScreenshotTest/ScreenshotTestRequestsListInner.vue';

const props = defineProps({
    screenshotResponse: {
        type: Object,
        required: true,
    },
    testCase: {
        type: Object,
        required: true,
    },
});
</script>

<style lang="scss" scoped>
h3 {
    margin: 1rem 0 0.5rem;
}
</style>
