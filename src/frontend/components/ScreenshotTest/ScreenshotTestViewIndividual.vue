<template>
    <menu id="links">
        <app-select
            id="brands-select"
            :options="screenshotStore.brands"
            :value="screenshotStore.selectedBrand"
            label="Select a brand"
            @change="screenshotStore.selectedBrand = $event"
        />

        <app-select
            id="pages-select"
            :formatter="pageOptionFormatter"
            :options="screenshotStore.pages"
            :value="screenshotStore.selectedTest"
            label="Select a page"
            @change="screenshotStore.selectedTest = $event"
        />

        <app-select
            id="drivers-select"
            :options="screenshotStore.drivers"
            :value="screenshotStore.selectedDriver"
            label="Select a driver"
            @change="screenshotStore.selectedDriver = $event"
        />

        <app-select
            id="browsers-select"
            :options="screenshotStore.browsers"
            :value="screenshotStore.selectedBrowser"
            label="Select a browser"
            @change="screenshotStore.selectedBrowser = $event"
        />

        <app-select
            id="viewports-select"
            :options="screenshotStore.viewports"
            :value="screenshotStore.selectedViewport"
            label="Select a viewport"
            @change="screenshotStore.selectedViewport = $event"
        />

        <app-select
            id="domains-select"
            :options="screenshotStore.domains"
            :value="screenshotStore.selectedDomain"
            label="Select a domain"
            @change="screenshotStore.selectedDomain = $event"
        />

        <li id="diff-toggle">
            <button @click="screenshotStore.diffVisible = !screenshotStore.diffVisible">
                Toggle overlay diff
            </button>
        </li>
    </menu>

    <div v-if="!selectedtestCase" class="no-test-case-message">
        <h3>No Test Case Found</h3>
        <p>Please select a valid combination of brand, page, viewport, browser, and driver.</p>
    </div>

    <template v-else>
        <screenshot-compare v-if="selectedtestCase.testCase" :test-case="selectedtestCase.testCase"/>

        <screenshot-test-info :test-case="selectedtestCase"/>

        <template v-for="(environment, envKey) in selectedtestCase.screenshotResponse" :key="envKey">
            <div v-if="environment.requests && (environment.requests.response || environment.requests.request)">
                <h3>{{ envKey }} Environment</h3>

                <screenshot-test-requests-list
                    v-if="environment.requests.response"
                    :requests="environment.requests.response"
                    title="Response URLs"
                />
                <screenshot-test-requests-list
                    v-if="environment.requests.request"
                    :requests="environment.requests.request"
                    title="Request URLs"
                />
            </div>
        </template>
    </template>
</template>

<script lang="ts" setup>
import ScreenshotCompare from '@/frontend/components/ScreenshotTest/ScreenshotCompare.vue';
import AppSelect from '@/frontend/components/Common/AppSelect.vue';
import {computed} from 'vue';
import ScreenshotTestInfo from '@/frontend/components/ScreenshotTest/ScreenshotTestInfo.vue';
import ScreenshotTestRequestsList from '@/frontend/components/ScreenshotTest/ScreenshotTestRequestsList.vue';
import {useScreenshotStore} from '@/frontend/stores/screenshotStore';

// Use the screenshot store
const screenshotStore = useScreenshotStore();

// Use the store's computed property for the selected test case
const selectedtestCase = computed(() => {
    // Find a valid test case if none is selected
    if (!screenshotStore.selectedTestCase) {
        screenshotStore.findValidTestCase();
    }

    return screenshotStore.selectedTestCase;
});

const pageOptionFormatter = (page) => page.url;
</script>

<style lang="scss" scoped>
#links {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    width: 100%;
    list-style-type: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border: 1px solid var(--color-border-primary);
    border-radius: var(--border-radius-lg);
    background-color: var(--color-background-secondary);
    margin-bottom: var(--spacing-lg);

    #diff-toggle {
        margin-left: auto;
    }

    li {
        display: flex;

        button {
            height: max-content;
            margin-top: auto;
            width: max-content;
            background-color: var(--color-primary);
            color: var(--color-text-inverse);
            border: none;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--border-radius-md);
            font-weight: var(--font-weight-medium);
            cursor: pointer;
            transition: all var(--transition-fast);

            &:hover {
                background-color: var(--color-primary-dark);
                transform: translateY(-1px);
            }

            &:active {
                transform: translateY(1px);
            }
        }

        a {
            margin-top: auto;
        }
    }
}

.no-test-case-message {
    padding: var(--spacing-xl);
    margin: var(--spacing-lg) 0;
    background-color: var(--color-background-secondary);
    border-radius: var(--border-radius-lg);
    text-align: center;

    h3 {
        color: var(--color-text-primary);
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-md);
        font-weight: var(--font-weight-semibold);
    }

    p {
        color: var(--color-text-secondary);
        font-size: var(--font-size-md);
        line-height: 1.5;
    }
}

@media (max-width: 768px) {
    #links {
        flex-direction: column;

        #diff-toggle {
            margin-left: 0;
            margin-top: var(--spacing-md);
        }
    }
}
</style>
