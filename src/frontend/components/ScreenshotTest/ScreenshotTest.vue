<template>
    <div class="container">
        <aside>
            <button
                :disabled="screenshotStore.viewType === 'run-test'"
                class="run-test-button"
                @click="handleRunTestClick"
            >
                Create New Test
            </button>
            <button
                :disabled="screenshotStore.viewType === 'individual'"
                @click="screenshotStore.setViewType('individual')"
            >
                Individual
            </button>
            <button
                :disabled="screenshotStore.viewType === 'thumbnail'"
                @click="screenshotStore.setViewType('thumbnail')"
            >
                Thumbnail
            </button>
            <button
                :disabled="screenshotStore.viewType === 'analytics'"
                @click="screenshotStore.setViewType('analytics')"
            >
                Analytics
            </button>

            <tests-performed-list
                v-if="screenshotStore.viewType !== 'analytics' && screenshotStore.viewType !== 'run-test'"
                :active-test="screenshotStore.selectedDate"
                :tests-performed="screenshotStore.performedTests"
                @change="screenshotStore.fetchConfig"
            />

            <button
                v-if="screenshotStore.viewType !== 'analytics' && screenshotStore.viewType !== 'run-test'"
                @click="screenshotStore.fetchDirectories"
            >
                Refresh...
            </button>
        </aside>

        <div id="test-view">
            <div v-if="screenshotStore.loading" class="loading-indicator">
                Loading data...
            </div>
            <screenshot-test-none-failing-message
                v-else-if="!screenshotStore.audit?.length && screenshotStore.viewType !== 'analytics' && screenshotStore.viewType !== 'run-test'"
            />
            <screenshot-test-view-individual
                v-else-if="screenshotStore.viewType === 'individual' && screenshotStore.audit?.length"
            />
            <screenshot-test-view-thumbnail
                v-else-if="screenshotStore.viewType === 'thumbnail' && screenshotStore.audit?.length"
            />
            <screenshot-test-view-analytics
                v-else-if="screenshotStore.viewType === 'analytics'"
            />
            <screenshot-test-view-run-test
                v-else-if="screenshotStore.viewType === 'run-test'"
            />
        </div>
    </div>
</template>

<script lang="ts" setup>
import {onMounted, watch} from 'vue';
import TestsPerformedList from '@/frontend/components/ScreenshotTest/TestsPerformedList.vue';
import ScreenshotTestNoneFailingMessage
    from '@/frontend/components/ScreenshotTest/ScreenshotTestNoneFailingMessage.vue';
import ScreenshotTestViewThumbnail from '@/frontend/components/ScreenshotTest/ScreenshotTestViewThumbnail.vue';
import ScreenshotTestViewIndividual from '@/frontend/components/ScreenshotTest/ScreenshotTestViewIndividual.vue';
import ScreenshotTestViewAnalytics from '@/frontend/components/ScreenshotTest/ScreenshotTestViewAnalytics.vue';
import ScreenshotTestViewRunTest from '@/frontend/components/ScreenshotTest/ScreenshotTestViewRunTest.vue';
import {useScreenshotStore} from '@/frontend/stores/screenshotStore';

// Use the screenshot store
const screenshotStore = useScreenshotStore();

// Flag to prevent multiple data loads
let dataLoaded = false;

// Method to handle the Run Test button click
function handleRunTestClick() {
    console.log('Changing view type to run-test');
    console.log('Current view type:', screenshotStore.viewType);

    // Use the store method to change the view type
    screenshotStore.setViewType('run-test');
}

// Watch for changes in the view type
watch(() => screenshotStore.viewType, (newValue, oldValue) => {
    console.log(`View type changed from ${oldValue} to ${newValue}`);
});

// Initialize data on component mount, but only once
onMounted(() => {
    if (!dataLoaded) {
        console.log('ScreenshotTest mounted, fetching directories...');
        screenshotStore.fetchDirectories().then(() => {
            // Initialize state from URL after directories are loaded
            screenshotStore.initFromUrl();
        });
        dataLoaded = true;
    } else {
        console.log('Data already loaded, skipping fetchDirectories call');
    }
});
</script>

<style lang="scss" scoped>
aside {
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    height: max-content;
    position: sticky;
    top: var(--spacing-md);
    background-color: var(--color-background-secondary);
    transition: all var(--transition-normal);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);

    button {
        padding: var(--spacing-sm);
        margin-top: 0;
        border: none;
        border-radius: var(--border-radius-md);
        width: 100%;
        cursor: pointer;
        background-color: var(--color-background-tertiary);
        color: var(--color-text-primary);
        font-weight: var(--font-weight-medium);
        font-size: var(--font-size-sm);
        transition: all var(--transition-fast);
        position: relative;
        overflow: hidden;

        &:hover:not(:disabled) {
            background-color: var(--color-background-tertiary);
            transform: translateY(-1px);
        }

        &:disabled {
            background-color: var(--color-primary);
            color: var(--color-text-inverse);
            cursor: default;
            font-weight: var(--font-weight-semibold);
        }

        &:focus {
            outline: none;
            box-shadow: 0 0 0 2px var(--color-primary-light);
        }

        &.run-test-button {
            background-color: var(--color-success, #10b981);
            color: var(--color-text-inverse);
            font-weight: var(--font-weight-semibold);
            text-align: center;

            &:hover:not(:disabled) {
                background-color: var(--color-secondary-dark, #34d399);
            }
        }
    }
}

.container {
    display: grid;
    grid-template-columns: 260px 1fr;
    grid-gap: var(--spacing-lg);
    max-width: 100vw;
    transition: all var(--transition-normal);

    #test-view {
        width: 100%;
        overflow: hidden; /* Prevent horizontal scrolling */

        .loading-indicator {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 300px;
            font-size: var(--font-size-lg);
            color: var(--color-text-secondary);
            background: var(--color-background-secondary);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            transition: all var(--transition-normal);
            position: relative;

            &::before {
                content: '';
                position: absolute;
                width: 40px;
                height: 40px;
                border-radius: 50%;
                border: 3px solid var(--color-border-secondary);
                border-top-color: var(--color-primary);
                animation: spin 1s linear infinite;
                margin-bottom: var(--spacing-lg);
            }

            &::after {
                content: 'Loading...';
                margin-top: 60px;
                font-weight: var(--font-weight-medium);
            }

            @keyframes spin {
                to {
                    transform: rotate(360deg);
                }
            }
        }
    }
}

@media (max-width: 1024px) {
    .container {
        grid-template-columns: 1fr;

        aside {
            position: static;
            margin-bottom: var(--spacing-lg);
            flex-direction: row;
            flex-wrap: wrap;
            gap: var(--spacing-xs);

            button {
                flex: 1;
                min-width: 120px;
                text-align: center;
            }
        }
    }
}
</style>
