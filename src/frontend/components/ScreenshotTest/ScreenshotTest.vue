<template>
    <div class="container">
        <aside>
            <button
                :disabled="screenshotStore.viewType === 'run-test'"
                class="run-test-button"
                @click="handleRunTestClick"
            >
                Create New Test
            </button>
            <button
                :disabled="screenshotStore.viewType === 'individual'"
                @click="screenshotStore.setViewType('individual')"
            >
                Individual
            </button>
            <button
                :disabled="screenshotStore.viewType === 'thumbnail'"
                @click="screenshotStore.setViewType('thumbnail')"
            >
                Thumbnail
            </button>
            <button
                :disabled="screenshotStore.viewType === 'analytics'"
                @click="screenshotStore.setViewType('analytics')"
            >
                Analytics
            </button>

            <tests-performed-list
                v-if="screenshotStore.viewType !== 'analytics' && screenshotStore.viewType !== 'run-test'"
                :active-test="screenshotStore.selectedDate"
                :tests-performed="screenshotStore.performedTests"
                @change="screenshotStore.fetchConfig"
            />

            <button
                v-if="screenshotStore.viewType !== 'analytics' && screenshotStore.viewType !== 'run-test'"
                @click="screenshotStore.fetchDirectories"
            >
                Refresh...
            </button>
        </aside>

        <div id="test-view">
            <!-- Error state -->
            <div v-if="error" class="error-indicator">
                <p>Error: {{ error }}</p>
                <button @click="initializeComponent">Retry</button>
            </div>

            <!-- Loading state -->
            <div v-else-if="screenshotStore.loading" class="loading-indicator">
                Loading data...
            </div>

            <!-- No failing tests message -->
            <screenshot-test-none-failing-message
                v-else-if="shouldShowNoFailingMessage"
            />

            <!-- Individual view -->
            <screenshot-test-view-individual
                v-else-if="shouldShowIndividualView"
            />

            <!-- Thumbnail view -->
            <screenshot-test-view-thumbnail
                v-else-if="shouldShowThumbnailView"
            />

            <!-- Analytics view -->
            <screenshot-test-view-analytics
                v-else-if="shouldShowAnalyticsView"
            />

            <!-- Run test view -->
            <screenshot-test-view-run-test
                v-else-if="shouldShowRunTestView"
            />
        </div>
    </div>
</template>

<script lang="ts" setup>
import {onMounted, watch, computed, ref} from 'vue';
import TestsPerformedList from '@/frontend/components/ScreenshotTest/TestsPerformedList.vue';
import ScreenshotTestNoneFailingMessage
    from '@/frontend/components/ScreenshotTest/ScreenshotTestNoneFailingMessage.vue';
import ScreenshotTestViewThumbnail from '@/frontend/components/ScreenshotTest/ScreenshotTestViewThumbnail.vue';
import ScreenshotTestViewIndividual from '@/frontend/components/ScreenshotTest/ScreenshotTestViewIndividual.vue';
import ScreenshotTestViewAnalytics from '@/frontend/components/ScreenshotTest/ScreenshotTestViewAnalytics.vue';
import ScreenshotTestViewRunTest from '@/frontend/components/ScreenshotTest/ScreenshotTestViewRunTest.vue';
import {useScreenshotStore} from '@/frontend/stores/screenshotStore';

// Use the screenshot store
const screenshotStore = useScreenshotStore();

// Reactive state for component
const isInitialized = ref(false);
const error = ref<string | null>(null);

// Computed properties for better reactivity
const hasAuditData = computed(() =>
    screenshotStore.audit && screenshotStore.audit.length > 0
);

const shouldShowNoFailingMessage = computed(() =>
    !screenshotStore.loading &&
    !hasAuditData.value &&
    screenshotStore.viewType !== 'analytics' &&
    screenshotStore.viewType !== 'run-test'
);

const shouldShowIndividualView = computed(() =>
    screenshotStore.viewType === 'individual' && hasAuditData.value
);

const shouldShowThumbnailView = computed(() =>
    screenshotStore.viewType === 'thumbnail' && hasAuditData.value
);

const shouldShowAnalyticsView = computed(() =>
    screenshotStore.viewType === 'analytics'
);

const shouldShowRunTestView = computed(() =>
    screenshotStore.viewType === 'run-test'
);

// Method to handle the Run Test button click
const handleRunTestClick = (): void => {
    try {
        screenshotStore.setViewType('run-test');
    } catch (err) {
        error.value = err instanceof Error ? err.message : 'Failed to change view';
        console.error('Error changing view type:', err);
    }
};

// Initialize component
const initializeComponent = async (): Promise<void> => {
    if (isInitialized.value) return;

    try {
        error.value = null;
        await screenshotStore.initFromUrl(new URLSearchParams(window.location.search));
        isInitialized.value = true;
    } catch (err) {
        error.value = err instanceof Error ? err.message : 'Failed to initialize component';
        console.error('Error initializing component:', err);
    }
};

// Watch for changes in the view type (for debugging)
watch(() => screenshotStore.viewType, (newValue, oldValue) => {
    console.log(`View type changed from ${oldValue} to ${newValue}`);
}, { immediate: true });

// Initialize component on mount
onMounted(async () => {
    await initializeComponent();
});
</script>

<style lang="scss" scoped>
aside {
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    height: max-content;
    position: sticky;
    top: var(--spacing-md);
    background-color: var(--color-background-secondary);
    transition: all var(--transition-normal);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);

    button {
        padding: var(--spacing-sm);
        margin-top: 0;
        border: none;
        border-radius: var(--border-radius-md);
        width: 100%;
        cursor: pointer;
        background-color: var(--color-background-tertiary);
        color: var(--color-text-primary);
        font-weight: var(--font-weight-medium);
        font-size: var(--font-size-sm);
        transition: all var(--transition-fast);
        position: relative;
        overflow: hidden;

        &:hover:not(:disabled) {
            background-color: var(--color-background-tertiary);
            transform: translateY(-1px);
        }

        &:disabled {
            background-color: var(--color-primary);
            color: var(--color-text-inverse);
            cursor: default;
            font-weight: var(--font-weight-semibold);
        }

        &:focus {
            outline: none;
            box-shadow: 0 0 0 2px var(--color-primary-light);
        }

        &.run-test-button {
            background-color: var(--color-success, #10b981);
            color: var(--color-text-inverse);
            font-weight: var(--font-weight-semibold);
            text-align: center;

            &:hover:not(:disabled) {
                background-color: var(--color-secondary-dark, #34d399);
            }
        }
    }
}

.container {
    display: grid;
    grid-template-columns: 260px 1fr;
    grid-gap: var(--spacing-lg);
    max-width: 100vw;
    transition: all var(--transition-normal);

    #test-view {
        width: 100%;
        overflow: hidden; /* Prevent horizontal scrolling */

        .loading-indicator,
        .error-indicator {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 300px;
            font-size: var(--font-size-lg);
            background: var(--color-background-secondary);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            transition: all var(--transition-normal);
            position: relative;
        }

        .loading-indicator {
            color: var(--color-text-secondary);

            &::before {
                content: '';
                position: absolute;
                width: 40px;
                height: 40px;
                border-radius: 50%;
                border: 3px solid var(--color-border-secondary);
                border-top-color: var(--color-primary);
                animation: spin 1s linear infinite;
                margin-bottom: var(--spacing-lg);
            }

            &::after {
                content: 'Loading...';
                margin-top: 60px;
                font-weight: var(--font-weight-medium);
            }

            @keyframes spin {
                to {
                    transform: rotate(360deg);
                }
            }
        }

        .error-indicator {
            color: var(--color-error, #ef4444);
            border: 2px solid var(--color-error, #ef4444);

            p {
                margin-bottom: var(--spacing-md);
                text-align: center;
            }

            button {
                padding: var(--spacing-sm) var(--spacing-md);
                background-color: var(--color-error, #ef4444);
                color: white;
                border: none;
                border-radius: var(--border-radius-md);
                cursor: pointer;
                font-weight: var(--font-weight-medium);
                transition: all var(--transition-fast);

                &:hover {
                    background-color: var(--color-error-dark, #dc2626);
                }
            }
        }
    }
}

@media (max-width: 1024px) {
    .container {
        grid-template-columns: 1fr;

        aside {
            position: static;
            margin-bottom: var(--spacing-lg);
            flex-direction: row;
            flex-wrap: wrap;
            gap: var(--spacing-xs);

            button {
                flex: 1;
                min-width: 120px;
                text-align: center;
            }
        }
    }
}
</style>
