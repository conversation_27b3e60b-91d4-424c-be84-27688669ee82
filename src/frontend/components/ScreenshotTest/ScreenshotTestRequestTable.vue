<template>
    <table>
        <thead>
        <tr>
            <th>Request</th>
            <th>Local</th>
            <th>Production</th>
            <th>Difference</th>
            <th>Status</th>
            <th></th>
        </tr>
        </thead>
        <tbody>
        <screenshot-test-request-row
            v-for="(testCase, index) in group"
            :key="index"
            :dataKey="dataKey"
            :testCase="testCase"
        />
        </tbody>
    </table>
</template>

<script lang="ts" setup>
import {defineProps} from 'vue';
import ScreenshotTestRequestRow from './ScreenshotTestRequestRow.vue';

const props = defineProps({
    group: {
        type: Array,
        required: true,
    },
    dataKey: {
        type: String,
        required: true,
    },
});
</script>

<style lang="scss" scoped>
table {
    width: 100%;
    text-align: left;
    margin-block: 1rem;
    line-break: anywhere;

    th, td {
        min-width: 6rem;
    }
}
</style>
