<template>
    <div class="charts-wrapper">
        <div v-if="loading" class="loading-message">
            Processing chart data...
        </div>
        <div v-else-if="!chartData.length" class="no-data-message">
            No matching data found. Try adjusting your filters.
        </div>
        <div v-else>
            <div v-for="(chart, index) in chartData" :key="index" class="chart-container">
                <h3 class="chart-title">{{ chart.group }} <span class="subtitle">({{ groupBy }})</span></h3>
                <canvas :id="`chart-${index}`" class="chart-canvas"></canvas>

                <div v-if="showUrlDetails && chart.urlCounts.length" class="url-details">
                    <h4>URL Breakdown:</h4>
                    <table>
                        <thead>
                        <tr>
                            <th>URL</th>
                            <th>Total Count</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="(urlData, urlIndex) in chart.urlCounts" :key="urlIndex">
                            <td class="url-cell">{{ urlData.url }}</td>
                            <td class="count-cell">{{ urlData.count }}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="chart-controls">
                <label>
                    <input v-model="showUrlDetails" type="checkbox"/>
                    Show URL details
                </label>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import {onBeforeUnmount, onMounted, ref, watch} from 'vue';
import Chart from 'chart.js/auto';

const props = defineProps({
    data: {
        type: Array,
        required: true,
    },
    groupBy: {
        type: String,
        default: 'brand',
    },
    loading: {
        type: Boolean,
        default: false,
    },
});

const chartData = ref([]);
const chartInstances = ref([]);
const showUrlDetails = ref(false);

// Parse the date string from format YYYY_MM_DD--HH:MM to a Date object
const parseDate = (dateStr) => {
    try {
        // Format: 2025-04-22--12:08
        const [datePart, timePart] = dateStr.split('--');
        const [year, month, day] = datePart.split('-').map(Number);
        const [hour, minute] = timePart.split(':').map(Number);

        // Month is 0-indexed in JavaScript Date
        return new Date(year, month - 1, day, hour, minute);
    } catch (e) {
        console.error('Error parsing date:', dateStr, e);
        return new Date();
    }
};

const getRandomColor = () => {
    // Predefined colors for better readability
    const colors = [
        '#4285F4', // Google Blue
        '#EA4335', // Google Red
        '#FBBC05', // Google Yellow
        '#34A853', // Google Green
        '#FF6D01', // Orange
        '#46BDC6', // Teal
        '#7B1FA2', // Purple
        '#0097A7', // Cyan
        '#689F38', // Light Green
        '#F57C00', // Dark Orange
        '#5D4037', // Brown
        '#757575', // Gray
    ];

    return colors[Math.floor(Math.random() * colors.length)];
};

const processChartData = () => {
    if (!props.data || props.data.length === 0) {
        chartData.value = [];
        return;
    }

    // Process the data for charts
    chartData.value = props.data.map(group => {
        // Sort data points by date
        const sortedData = [...group.data].sort((a, b) => {
            return parseDate(a.date).getTime() - parseDate(b.date).getTime();
        });

        // Collect all unique URLs across all dates
        const allUrls = new Set();
        sortedData.forEach(point => {
            Object.keys(point.urls).forEach(url => allUrls.add(url));
        });

        // Calculate total counts per URL
        const urlCounts = Array.from(allUrls).map(url => {
            const count = sortedData.reduce((sum, point) => sum + (point.urls[url] || 0), 0);
            return {url, count};
        }).sort((a, b) => b.count - a.count); // Sort by count descending

        return {
            group: group.group,
            data: sortedData,
            urlCounts,
        };
    });
};

const createCharts = () => {
    // Clear any existing chart instances
    chartInstances.value.forEach(chart => chart.destroy());
    chartInstances.value = [];

    // Create new charts
    chartData.value.forEach((chart, index) => {
        const canvas = document.getElementById(`chart-${index}`);
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        // Prepare data for the chart
        const labels = chart.data.map(point => parseDate(point.date));
        const datasets = [{
            label: `Total ${props.groupBy === 'brand' ? 'Brand' : props.groupBy === 'device' ? 'Device' : 'Driver'} Requests`,
            data: chart.data.map(point => point.count),
            borderColor: getRandomColor(),
            backgroundColor: 'rgba(66, 133, 244, 0.2)',
            borderWidth: 2,
            tension: 0.1,
            fill: true,
        }];

        // Create the chart
        const chartInstance = new Chart(ctx, {
            type: 'line',
            data: {labels, datasets},
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: false,
                    },
                    tooltip: {
                        callbacks: {
                            title: (tooltipItems) => {
                                const date = tooltipItems[0].label;
                                return new Date(date).toLocaleString();
                            },
                        },
                    },
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'day',
                            displayFormats: {
                                day: 'MMM d, yyyy',
                            },
                        },
                        title: {
                            display: true,
                            text: 'Date',
                        },
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Requests',
                        },
                    },
                },
            },
        });

        chartInstances.value.push(chartInstance);
    });
};

watch(() => props.data, () => {
    processChartData();
    // Use nextTick to ensure the DOM is updated before creating charts
    setTimeout(createCharts, 100);
}, {deep: true});

watch(() => props.groupBy, () => {
    // When groupBy changes, charts will be recreated
    setTimeout(createCharts, 100);
});

onMounted(() => {
    processChartData();
    setTimeout(createCharts, 100);
});

onBeforeUnmount(() => {
    // Clean up chart instances
    chartInstances.value.forEach(chart => chart.destroy());
});
</script>

<style lang="scss" scoped>
.charts-wrapper {
    width: 100%;

    .loading-message,
    .no-data-message {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
        font-size: 1.2rem;
        color: #666;
        background: #f9f9f9;
        border-radius: 8px;
    }

    .chart-container {
        margin-bottom: 2rem;
        padding: 1rem;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .chart-title {
            margin-top: 0;
            margin-bottom: 1rem;
            font-size: 1.5rem;
            color: #333;

            .subtitle {
                font-size: 1rem;
                color: #666;
                font-weight: normal;
            }
        }

        .chart-canvas {
            height: 300px;
            width: 100%;
        }
    }

    .url-details {
        margin-top: 1.5rem;
        padding-top: 1rem;
        border-top: 1px solid #eee;

        h4 {
            margin-top: 0;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
            color: #555;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;

            th, td {
                padding: 0.5rem;
                text-align: left;
                border-bottom: 1px solid #eee;
            }

            th {
                font-weight: bold;
                color: #333;
                background: #f5f5f5;
            }

            .url-cell {
                max-width: 500px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .count-cell {
                text-align: right;
                font-weight: bold;
            }
        }
    }

    .chart-controls {
        margin-top: 1rem;
        display: flex;
        justify-content: flex-end;

        label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            user-select: none;

            input[type="checkbox"] {
                margin: 0;
            }
        }
    }
}
</style>
