<script setup>
import {ref} from 'vue';

defineProps({
    testsPerformed: {
        required: false,
        type: Array
    },
    activeTest: {
        required: false,
        type: String,
    },
});

// Flag to prevent multiple rapid clicks
const isChanging = ref(false);

// Handle test change with debounce
function handleChange(test) {
    if (isChanging.value) return;

    isChanging.value = true;

    // Emit the change event
    console.log(`Changing to test: ${test}`);
    emit('change', test);

    // Reset the flag after a short delay
    setTimeout(() => {
        isChanging.value = false;
    }, 500);
}

const emit = defineEmits(['change']);
</script>

<template>
    <menu id="tests-performed-list">
        <li v-for="test in testsPerformed"
            :key="test"
            :class="{ active: test === activeTest, 'disabled': isChanging }"
            @click="handleChange(test)">
            {{ test.replaceAll('--', ' ') }}
        </li>
    </menu>
</template>

<style lang="scss" scoped>
menu {
    display: flex;
    flex-flow: column;
    gap: .8rem;
    list-style-type: none;
    padding: 0;

    li {
        cursor: pointer;
        user-select: none;
        transition: all var(--transition-fast);

        &:hover:not(.disabled) {
            text-decoration: underline;
        }

        &.active {
            color: var(--color-primary);
            font-weight: var(--font-weight-medium);
        }

        &.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    }
}
</style>
