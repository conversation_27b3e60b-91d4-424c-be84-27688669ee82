<template>
    <div class="success-message">
        <div class="success-content">
            <div class="success-icon">
                <svg fill="currentColor" height="48" viewBox="0 0 24 24" width="48" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
            </div>
            <div class="success-text">
                <h2>All Tests Passed</h2>
                <p>All screenshot tests have completed successfully without any visual regressions.</p>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
// This component displays a success message when all tests have passed
// It uses CSS variables for styling to maintain consistency with the rest of the application
</script>

<style lang="scss" scoped>
.success-message {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-xl);
    margin: var(--spacing-xl) 0;
    background-color: var(--color-background-secondary);
    border-radius: var(--border-radius-lg);
    transition: all var(--transition-normal);

    .success-content {
        display: flex;
        align-items: center;
        max-width: 600px;
        animation: fadeIn 0.5s ease-in-out;

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .success-icon {
            margin-right: var(--spacing-lg);
            color: var(--color-success);
            display: flex;
            align-items: center;
            justify-content: center;

            svg {
                transition: transform var(--transition-normal);
                animation: checkmark 0.8s ease-in-out forwards;
                transform-origin: center;
            }

            @keyframes checkmark {
                0% {
                    transform: scale(0);
                    opacity: 0;
                }
                50% {
                    transform: scale(1.2);
                    opacity: 1;
                }
                70% {
                    transform: scale(0.9);
                }
                100% {
                    transform: scale(1);
                }
            }

            &:hover svg {
                transform: scale(1.1);
            }
        }

        .success-text {
            h2 {
                font-size: var(--font-size-2xl);
                font-weight: var(--font-weight-semibold);
                color: var(--color-success);
                margin: 0 0 var(--spacing-sm) 0;
            }

            p {
                font-size: var(--font-size-md);
                color: var(--color-text-secondary);
                margin: 0;
                line-height: 1.5;
            }
        }
    }
}

@media (max-width: 768px) {
    .success-message {
        padding: var(--spacing-lg);

        .success-content {
            flex-direction: column;
            text-align: center;

            .success-icon {
                margin-right: 0;
                margin-bottom: var(--spacing-md);
            }
        }
    }
}
</style>