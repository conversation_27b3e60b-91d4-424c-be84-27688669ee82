<template>
    <div class="test-run-form">
        <h2>Create New Test Run</h2>

        <div class="form-content">
            <div class="form-section">
                <h3>Test Configuration</h3>

                <div class="form-group">
                    <label for="username-input">Username</label>
                    <input id="username-input" v-model="username" placeholder="Your username" type="text">
                </div>

                <div class="form-group">
                    <label>Brands</label>
                    <div class="checkbox-group">
                        <label v-for="brand in availableBrands" :key="brand">
                            <input v-model="selectedBrands" :value="brand" type="checkbox">
                            {{ brand }}
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label>Drivers</label>
                    <div class="checkbox-group">
                        <label>
                            <input v-model="selectedDrivers" type="checkbox" value="playwright">
                            Playwright
                        </label>
                        <label>
                            <input v-model="selectedDrivers" type="checkbox" value="puppeteer">
                            Puppeteer
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <h3>Test Scope</h3>

                <div class="form-group">
                    <label>Viewports</label>
                    <div class="checkbox-group">
                        <label v-for="viewport in availableViewports" :key="viewport.name">
                            <input v-model="selectedViewports" :value="viewport.name" type="checkbox">
                            {{ viewport.name }} ({{ viewport.width }}x{{ viewport.height }})
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label>Browsers</label>
                    <div class="checkbox-group">
                        <label v-for="browser in availableBrowsers" :key="browser.product">
                            <input v-model="selectedBrowsers" :value="browser.product" type="checkbox">
                            {{ browser.displayName }}
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <label>URLs</label>
                    <div class="checkbox-group">
                        <label v-for="route in availableRoutes" :key="route.value">
                            <input v-model="selectedUrls" :value="route.value" type="checkbox">
                            {{ route.name }}
                        </label>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <h3>JSON Configuration</h3>
                <div class="form-group">
                    <label for="json-config">Paste JSON Configuration</label>
                    <textarea
                        id="json-config"
                        v-model="jsonConfig"
                        placeholder="Paste JSON configuration here..."
                        rows="10"
                    ></textarea>
                    <button class="apply-json-button" @click="applyJsonConfig">Apply JSON</button>
                </div>
            </div>

            <div class="form-actions">
                <button class="cancel-button" @click="goBack">Cancel</button>
                <button :disabled="isRunning || !isFormValid" class="run-button" @click="runTest">
                    {{ isRunning ? 'Running...' : 'Run Test' }}
                </button>
            </div>
        </div>

        <div v-if="testOutput" class="test-output">
            <h3>Test Output</h3>
            <pre>{{ testOutput }}</pre>
        </div>
    </div>
</template>

<script lang="ts" setup>
import {computed, onMounted, ref} from 'vue';
import {useScreenshotStore} from '@/frontend/stores/screenshotStore';
import useLocalstorageRef from '@/frontend/customRefs/localstorageRef.js';
import routes from '@/shared/config/routes';
import brands from '@/shared/config/brands/brands.json';
import browsers from '@/shared/config/browsers';
import viewports from '@/shared/config/viewports';

// Store
const screenshotStore = useScreenshotStore();

// Form state
const selectedBrands = ref<string[]>([]);
const selectedDrivers = ref<string[]>([]);
const selectedViewports = ref<string[]>([]);
const selectedBrowsers = ref<string[]>([]);
const selectedUrls = ref<string[]>([]);
const isRunning = ref(false);
const testOutput = ref('');
const jsonConfig = ref('');

// Use localStorage for username
const username = useLocalstorageRef('oracle--test-username', '');

// Available options
const availableBrands = computed(() => {
    return brands?.brands.map(brand => brand.slug);
});

const availableViewports = computed(() => {
    return viewports.map(viewport => ({
        name: viewport.name,
        width: viewport.width,
        height: viewport.height,
    }));
});

const availableBrowsers = computed(() => {
    return browsers.map(browser => ({
        displayName: browser.displayName,
        product: browser.product,
    }));
});

const availableRoutes = computed(() => {
    return routes.map(route => ({
        value: route.value,
        name: route.name.replace(/\//g, ''),
    }));
});

// Form validation
const isFormValid = computed(() => {
    return (
        selectedBrands.value.length > 0 &&
        selectedDrivers.value.length > 0 &&
        selectedViewports.value.length > 0 &&
        selectedBrowsers.value.length > 0 &&
        selectedUrls.value.length > 0 &&
        username.value
    );
});

// Initialize form with default values
onMounted(() => {
    console.log('ScreenshotTestViewRunTest component mounted');
    console.log('Available brands:', availableBrands.value);
    // Set default values
    if (availableBrands.value.length > 0) {
        selectedBrands.value = [availableBrands.value[0]];
    }

    selectedDrivers.value = ['playwright'];
    selectedViewports.value = ['desktop'];
    selectedBrowsers.value = ['chrome'];

    // Set default URLs
    if (availableRoutes.value.length > 0) {
        selectedUrls.value = [availableRoutes.value[0].value];
    }

    // Set empty JSON object by default
    jsonConfig.value = JSON.stringify({}, null, 4);
});

// Methods
function goBack() {
    screenshotStore.setViewType('thumbnail');
}

function applyJsonConfig() {
    try {
        const config = JSON.parse(jsonConfig.value);

        // Apply the configuration
        if (config.user) {
            username.value = config.user;
        }

        if (config.brands && config.brands.length > 0) {
            // Filter brands to only include available ones
            selectedBrands.value = config.brands.filter(brand =>
                availableBrands.value.includes(brand),
            );

            if (selectedBrands.value.length === 0 && availableBrands.value.length > 0) {
                // If no valid brands were found, use the first available brand
                selectedBrands.value = [availableBrands.value[0]];
                console.warn('No valid brands found in JSON, using first available brand');
            }
        }

        if (config.viewports && config.viewports.length > 0) {
            selectedViewports.value = config.viewports.filter(viewport =>
                availableViewports.some(v => v.name === viewport),
            );
        }

        if (config.browsers && config.browsers.length > 0) {
            selectedBrowsers.value = config.browsers.filter(browser =>
                availableBrowsers.some(b => b.product === browser),
            );
        }

        if (config.urls && config.urls.length > 0) {
            selectedUrls.value = config.urls.filter(url =>
                availableRoutes.value.some(r => r.value === url),
            );

            // If no valid URLs were found, use the first available URL
            if (selectedUrls.value.length === 0 && availableRoutes.value.length > 0) {
                selectedUrls.value = [availableRoutes.value[0].value];
                console.warn('No valid URLs found in JSON, using first available URL');
            }
        }

        alert('JSON configuration applied successfully!');
    } catch (error) {
        console.error('Error parsing JSON:', error);
        alert(`Error parsing JSON: ${error.message}`);
    }
}

async function runTest() {
    if (!isFormValid.value || isRunning.value) return;

    isRunning.value = true;
    testOutput.value = 'Starting test run...\n';

    try {
        // Build the command
        const command = buildTestCommand();
        testOutput.value += `Executing command: ${command}\n\n`;

        // Make API call to run the test
        const response = await fetch('/api/run-test', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                command,
                brands: selectedBrands.value,
                driver: selectedDrivers.value,
                viewports: selectedViewports.value,
                browsers: selectedBrowsers.value,
                urls: selectedUrls.value,
                username: username.value,
            }),
        });

        if (!response.ok) {
            throw new Error(`Server responded with status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            testOutput.value += `Test completed successfully!\n\n${result.output}`;

            // Refresh the test data
            await screenshotStore.fetchDirectories();
        } else {
            testOutput.value += `Test failed: ${result.error}\n\n${result.output}`;
        }
    } catch (error) {
        console.error('Error running test:', error);
        testOutput.value += `Error: ${error.message}\n`;
    } finally {
        isRunning.value = false;
    }
}

function buildTestCommand() {
    const parts = [
        `--brands=${selectedBrands.value.join(',')}`,
        `--drivers=${selectedDrivers.value.join(',')}`,
        `--viewports=${selectedViewports.value.join(',')}`,
        `--browsers=${selectedBrowsers.value.join(',')}`,
        `--urls=${selectedUrls.value.join(',')}`,
        `--username=${username.value}`,
    ];

    return `yarn test:frontend ${parts.join(' ')}`;
}
</script>

<style lang="scss" scoped>
.test-run-form {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-lg);

    h2 {
        margin-top: 0;
        margin-bottom: var(--spacing-lg);
        font-size: var(--font-size-2xl);
        font-weight: var(--font-weight-semibold);
        color: var(--color-text-primary);
        border-bottom: 1px solid var(--color-border-primary);
        padding-bottom: var(--spacing-md);
    }

    h3 {
        margin-top: 0;
        margin-bottom: var(--spacing-md);
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-medium);
        color: var(--color-text-primary);
    }

    .form-content {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
    }

    .form-section {
        background-color: var(--color-background-secondary);
        padding: var(--spacing-lg);
        border-radius: var(--border-radius-md);
        box-shadow: var(--shadow-sm);
    }

    .form-group {
        margin-bottom: var(--spacing-md);

        label {
            display: block;
            margin-bottom: var(--spacing-xs);
            font-weight: var(--font-weight-medium);
            color: var(--color-text-primary);
        }

        select, input[type="text"], textarea {
            width: 100%;
            padding: var(--spacing-sm);
            border: 1px solid var(--color-border-primary);
            border-radius: var(--border-radius-sm);
            background-color: var(--color-background-primary);
            color: var(--color-text-primary);
            font-size: var(--font-size-md);

            &:focus {
                outline: none;
                border-color: var(--color-primary);
                box-shadow: 0 0 0 2px var(--color-primary-light);
            }
        }

        textarea {
            font-family: monospace;
            font-size: var(--font-size-sm);
            resize: vertical;
        }

        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
            max-height: 200px;
            overflow-y: auto;
            padding: var(--spacing-sm);
            background-color: var(--color-background-primary);
            border: 1px solid var(--color-border-primary);
            border-radius: var(--border-radius-sm);

            label {
                display: flex;
                align-items: center;
                gap: var(--spacing-xs);
                font-weight: var(--font-weight-normal);
                cursor: pointer;
                margin: 0;
                padding: var(--spacing-xs) 0;

                &:hover {
                    background-color: var(--color-background-secondary);
                }

                input[type="checkbox"] {
                    margin: 0;
                    cursor: pointer;
                }
            }
        }

        .apply-json-button {
            margin-top: var(--spacing-sm);
            padding: var(--spacing-xs) var(--spacing-md);
            background-color: var(--color-background-tertiary);
            color: var(--color-text-primary);
            border: 1px solid var(--color-border-primary);
            border-radius: var(--border-radius-sm);
            cursor: pointer;
            font-size: var(--font-size-sm);

            &:hover {
                background-color: var(--color-background-quaternary);
            }
        }
    }

    .form-actions {
        grid-column: 1 / -1;
        display: flex;
        justify-content: flex-end;
        gap: var(--spacing-md);
        margin-top: var(--spacing-lg);
        padding-top: var(--spacing-md);
        border-top: 1px solid var(--color-border-primary);

        button {
            padding: var(--spacing-sm) var(--spacing-lg);
            border-radius: var(--border-radius-md);
            font-weight: var(--font-weight-medium);
            cursor: pointer;
            transition: all var(--transition-fast);

            &.cancel-button {
                background-color: var(--color-background-secondary);
                color: var(--color-text-primary);
                border: 1px solid var(--color-border-primary);

                &:hover {
                    background-color: var(--color-background-tertiary);
                }
            }

            &.run-button {
                background-color: var(--color-primary);
                color: var(--color-text-inverse);
                border: none;

                &:hover:not(:disabled) {
                    background-color: var(--color-primary-dark);
                }

                &:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                }
            }
        }
    }

    .test-output {
        margin-top: var(--spacing-xl);
        padding: var(--spacing-md);
        background-color: var(--color-background-secondary);
        border-radius: var(--border-radius-md);

        h3 {
            margin-top: 0;
            margin-bottom: var(--spacing-sm);
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
        }

        pre {
            margin: 0;
            padding: var(--spacing-md);
            background-color: var(--color-background-tertiary);
            border-radius: var(--border-radius-sm);
            font-family: monospace;
            font-size: var(--font-size-sm);
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 400px;
            overflow-y: auto;
        }
    }
}

@media (max-width: 768px) {
    .test-run-form {
        padding: var(--spacing-md);

        .form-content {
            grid-template-columns: 1fr;
            gap: var(--spacing-lg);
        }
    }
}
</style>
