<template>
    <header>
        <app-title>
            <logo/>
            Oracle
        </app-title>

        <nav class="header-nav">
            <router-link to="/" class="nav-link" active-class="active">
                Screenshots
            </router-link>
        </nav>

        <div class="header-controls">
            <app-select
                v-if="showViewSelector"
                v-model="currentView"
                :options="viewOptions"
                label="View"
                size="sm"
                @update:model-value="handleViewChange"
            />

            <theme-toggle/>

            <AppButton
                variant="primary"
                size="sm"
                @click="handleRunTestClick"
            >
                Run Test
            </AppButton>
        </div>
    </header>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import AppTitle from "@/frontend/components/Common/AppTitle.vue";
import ThemeToggle from "@/frontend/components/ThemeToggle.vue";
import Logo from "@/frontend/components/Logo.vue";
import AppSelect from "@/frontend/components/Common/AppSelect.vue";
import AppButton from "@/frontend/components/ui/AppButton.vue";
import { useScreenshotStore } from '@/frontend/stores/modules/screenshotStore.ts';

const route = useRoute();
const screenshotStore = useScreenshotStore();

// Show view selector only on home page
const showViewSelector = computed(() => route.name === 'home');

const currentView = computed({
  get: () => screenshotStore.view.type,
  set: (value) => screenshotStore.setViewType(value)
});

const viewOptions = [
  { value: 'thumbnail', label: 'Thumbnail' },
  { value: 'individual', label: 'Individual' },
  { value: 'analytics', label: 'Analytics' },
  { value: 'run-test', label: 'Run Test' }
];

const handleViewChange = (newView: string) => {
  screenshotStore.setViewType(newView as any);
};

const handleRunTestClick = () => {
  screenshotStore.setViewType('run-test');
};
</script>

<style lang="scss" scoped>
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--color-background-primary);
    border-bottom: 1px solid var(--color-border-primary);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-nav {
    display: flex;
    gap: var(--spacing-md);

    .nav-link {
        padding: var(--spacing-sm) var(--spacing-md);
        text-decoration: none;
        color: var(--color-text-secondary);
        font-weight: var(--font-weight-medium);
        border-radius: var(--border-radius-md);
        transition: all var(--transition-fast);

        &:hover {
            color: var(--color-text-primary);
            background-color: var(--color-background-secondary);
        }

        &.active {
            color: var(--color-primary);
            background-color: var(--color-primary-light, rgba(59, 130, 246, 0.1));
        }
    }
}

.header-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

@media (max-width: 768px) {
    header {
        flex-direction: column;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .header-controls {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
}
</style>