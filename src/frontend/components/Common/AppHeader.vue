<template>
    <header>
        <app-title>
            <logo/>
            Oracle
        </app-title>

        <theme-toggle/>
    </header>
</template>

<script setup>
import AppTitle from "@/frontend/components/Common/AppTitle.vue";
import ThemeToggle from "@/frontend/components/ThemeToggle.vue";
import Logo from "@/frontend/components/Logo.vue";
</script>

<style lang="scss" scoped>
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-inline: 2rem;
}
</style>