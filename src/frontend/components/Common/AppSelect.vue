<template>
    <div v-if="options?.length > 0">
        <label :for="id">{{ label }}</label>
        <select :id="id" :value="value" @change="$emit('change', $event.target.value)">
            <option value="">Select an option</option>
            <option v-for="(option, index) in options" :key="index" :value="formatString(option)">
                {{ formatString(option) }}
            </option>
        </select>
    </div>
</template>

<script setup>
import {defineProps} from 'vue';

defineEmits([
    'change',
]);
const props = defineProps({
    label: {
        type: String,
        required: true,
    },
    options: {
        type: Array,
        required: false,
        default: () => []
    },
    value: {
        type: [String, Number],
        required: false,
    },
    id: {
        type: String,
        required: true,
    },
    formatter: {
        required: false,
    },
});

const formatString = (string) => {
    return typeof props.formatter === 'function' ? props.formatter(string) : string;
}
</script>

<style scoped>
select {
    padding: 0.5rem;
    margin-top: 0.3rem;
    border: 1px solid #ccc;
    border-radius: 0.5rem;
    width: 100%;
}

label {
    font-weight: bold;
}

option {
    padding: 0.3rem;
}
</style>
