<template>
    <span :class="statusClass">{{ statusText }}</span>
</template>

<script>
export default {
    name: 'StatusIndicator',
    props: {
        passing: Boolean
    },
    computed: {
        statusText() {
            return this.passing ? 'No Difference' : 'Mismatch';
        },
        statusClass() {
            return this.passing ? 'status-matched' : 'status-mismatched';
        }
    }
};
</script>

<style scoped>
.status-matched {
    color: green;
}

.status-mismatched {
    color: red;
}
</style>
