<template>
  <div class="test-runner">
    <!-- Test Configuration Form -->
    <div class="test-config-section">
      <h2>Create New Test</h2>
      
      <form @submit.prevent="handleSubmit" class="test-form">
        <!-- Brands Selection -->
        <div class="form-group">
          <label class="form-label">Brands</label>
          <div class="checkbox-group">
            <label v-for="brand in availableBrands" :key="brand" class="checkbox-item">
              <input
                type="checkbox"
                :value="brand"
                v-model="selectedBrands"
              />
              <span>{{ brand }}</span>
            </label>
          </div>
        </div>

        <!-- URLs Selection -->
        <div class="form-group">
          <label class="form-label">URLs</label>
          <div class="checkbox-group">
            <label v-for="route in availableRoutes" :key="route.value" class="checkbox-item">
              <input
                type="checkbox"
                :value="route.value"
                v-model="selectedUrls"
              />
              <span>{{ route.name }} ({{ route.value }})</span>
            </label>
          </div>
        </div>

        <!-- Browsers Selection -->
        <div class="form-group">
          <label class="form-label">Browsers</label>
          <div class="checkbox-group">
            <label v-for="browser in availableBrowsers" :key="browser.product" class="checkbox-item">
              <input
                type="checkbox"
                :value="browser.product"
                v-model="selectedBrowsers"
              />
              <span>{{ browser.displayName || browser.product }}</span>
            </label>
          </div>
        </div>

        <!-- Viewports Selection -->
        <div class="form-group">
          <label class="form-label">Viewports</label>
          <div class="checkbox-group">
            <label v-for="viewport in availableViewports" :key="viewport.name" class="checkbox-item">
              <input
                type="checkbox"
                :value="viewport.name"
                v-model="selectedViewports"
              />
              <span>{{ viewport.name }} ({{ viewport.width }}x{{ viewport.height }})</span>
            </label>
          </div>
        </div>

        <!-- Drivers Selection -->
        <div class="form-group">
          <label class="form-label">Drivers</label>
          <div class="checkbox-group">
            <label v-for="driver in availableDrivers" :key="driver" class="checkbox-item">
              <input
                type="checkbox"
                :value="driver"
                v-model="selectedDrivers"
              />
              <span>{{ driver }}</span>
            </label>
          </div>
        </div>

        <!-- Username -->
        <div class="form-group">
          <label class="form-label" for="username">Username</label>
          <input
            id="username"
            type="text"
            v-model="username"
            class="form-input"
            placeholder="Enter your username"
            required
          />
        </div>

        <!-- Advanced Options -->
        <details class="advanced-options">
          <summary>Advanced Options</summary>
          
          <div class="form-group">
            <label class="form-label">Environments</label>
            <div class="checkbox-group">
              <label class="checkbox-item">
                <input type="checkbox" value="local" v-model="selectedEnvironments" />
                <span>Local</span>
              </label>
              <label class="checkbox-item">
                <input type="checkbox" value="production" v-model="selectedEnvironments" />
                <span>Production</span>
              </label>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label" for="concurrency">Max Concurrency</label>
            <input
              id="concurrency"
              type="number"
              v-model.number="maxConcurrency"
              class="form-input"
              min="1"
              max="50"
            />
          </div>
        </details>

        <!-- Validation Errors -->
        <div v-if="validationErrors.length > 0" class="validation-errors">
          <h4>Please fix the following errors:</h4>
          <ul>
            <li v-for="error in validationErrors" :key="error">{{ error }}</li>
          </ul>
        </div>

        <!-- Estimated Time -->
        <div v-if="estimatedTime > 0" class="estimated-time">
          <strong>Estimated execution time: {{ formatTime(estimatedTime) }}</strong>
        </div>

        <!-- Submit Button -->
        <div class="form-actions">
          <AppButton
            type="submit"
            variant="primary"
            size="lg"
            :loading="testRunStore.isRunning"
            :disabled="!isFormValid"
            full-width
          >
            {{ testRunStore.isRunning ? 'Running Test...' : 'Start Test' }}
          </AppButton>

          <AppButton
            v-if="testRunStore.isRunning"
            variant="danger"
            size="lg"
            @click="stopTest"
            full-width
          >
            Stop Test
          </AppButton>
        </div>
      </form>
    </div>

    <!-- Real-time Test Monitoring -->
    <div v-if="testRunStore.isRunning || testRunStore.hasResults" class="test-monitoring-section">
      <div class="monitor-header">
        <h3>Test Execution Monitor</h3>
        <div class="connection-status" :class="connectionStatus">
          <span class="status-indicator"></span>
          <span class="status-text">{{ connectionStatus }}</span>
        </div>
      </div>
      
      <!-- Progress Bar -->
      <div v-if="testRunStore.isRunning" class="progress-section">
        <div class="progress-header">
          <span class="progress-label">{{ testRunStore.state.currentStep }}</span>
          <span class="progress-percentage">{{ Math.round(testRunStore.progressPercentage) }}%</span>
        </div>
        
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: `${testRunStore.progressPercentage}%` }"
          ></div>
        </div>

        <div class="progress-info">
          <span>Elapsed: {{ formatTime(testRunStore.elapsedTime) }}</span>
          <span v-if="testRunStore.remainingTime">
            Remaining: {{ formatTime(testRunStore.remainingTime) }}
          </span>
        </div>
      </div>

      <!-- Live Logs -->
      <div class="logs-section">
        <div class="logs-header">
          <h4>Live Logs</h4>
          <AppButton size="sm" variant="ghost" @click="clearLogs">
            Clear
          </AppButton>
        </div>
        
        <div class="logs-container" ref="logsContainer">
          <div
            v-for="(log, index) in testRunStore.state.logs"
            :key="index"
            class="log-entry"
          >
            {{ log }}
          </div>
          <div v-if="testRunStore.state.logs.length === 0" class="no-logs">
            No logs yet...
          </div>
        </div>
      </div>

      <!-- Test Results -->
      <div v-if="testRunStore.lastResult" class="results-section">
        <h4>Last Test Result</h4>
        <div class="result-card" :class="{ 'success': testRunStore.lastResult.success, 'error': !testRunStore.lastResult.success }">
          <div class="result-header">
            <span class="result-status">
              {{ testRunStore.lastResult.success ? '✓ Success' : '✗ Failed' }}
            </span>
            <span class="result-date">{{ formatDate(testRunStore.lastResult.date) }}</span>
          </div>
          
          <div class="result-details">
            <div class="result-item">
              <strong>Test Cases:</strong> {{ testRunStore.lastResult.totalTestCases }}
            </div>
            <div class="result-item">
              <strong>Execution Time:</strong> {{ formatTime(testRunStore.lastResult.executionTime) }}
            </div>
            <div v-if="testRunStore.lastResult.error" class="result-item error">
              <strong>Error:</strong> {{ testRunStore.lastResult.error }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import { useTestRunStore } from '@/frontend/stores/modules/testRunStore.ts';
import { useAppStore } from '@/frontend/stores/modules/appStore.ts';
import { useWebSocket } from '@/frontend/composables/useWebSocket.ts';
import AppButton from '@/frontend/components/ui/AppButton.vue';
import routes from '@/shared/config/routes.ts';
import brands from '@/shared/config/brands/brands.json';
import browsers from '@/shared/config/browsers.ts';
import viewports from '@/shared/config/viewports.ts';

const testRunStore = useTestRunStore();
const appStore = useAppStore();
const { isConnected, connectionStatus } = useWebSocket();

// Form data
const selectedBrands = ref<string[]>([]);
const selectedUrls = ref<string[]>(['/']);
const selectedBrowsers = ref<string[]>(['chrome']);
const selectedViewports = ref<string[]>(['desktop']);
const selectedDrivers = ref<string[]>(['puppeteer']);
const selectedEnvironments = ref<string[]>(['local', 'production']);
const username = ref('');
const maxConcurrency = ref(25);

// UI state
const validationErrors = ref<string[]>([]);
const logsContainer = ref<HTMLElement>();

// Available options
const availableBrands = computed(() => brands?.brands?.map(brand => brand.slug) || []);
const availableRoutes = computed(() => routes || []);
const availableBrowsers = computed(() => browsers || []);
const availableViewports = computed(() => viewports || []);
const availableDrivers = ['puppeteer', 'playwright'];

// Validation
const isFormValid = computed(() => {
  return selectedBrands.value.length > 0 &&
         selectedUrls.value.length > 0 &&
         selectedBrowsers.value.length > 0 &&
         selectedViewports.value.length > 0 &&
         selectedDrivers.value.length > 0 &&
         username.value.trim() !== '' &&
         validationErrors.value.length === 0;
});

const estimatedTime = computed(() => {
  if (!isFormValid.value) return 0;
  
  const totalCombinations = 
    selectedBrands.value.length *
    selectedUrls.value.length *
    selectedBrowsers.value.length *
    selectedViewports.value.length *
    selectedDrivers.value.length *
    selectedEnvironments.value.length;

  return totalCombinations * 5000; // 5 seconds per combination
});

// Validation logic
const validateForm = () => {
  const errors: string[] = [];

  if (selectedBrands.value.length === 0) errors.push('At least one brand must be selected');
  if (selectedUrls.value.length === 0) errors.push('At least one URL must be selected');
  if (selectedBrowsers.value.length === 0) errors.push('At least one browser must be selected');
  if (selectedViewports.value.length === 0) errors.push('At least one viewport must be selected');
  if (selectedDrivers.value.length === 0) errors.push('At least one driver must be selected');
  if (selectedEnvironments.value.length === 0) errors.push('At least one environment must be selected');
  if (!username.value.trim()) errors.push('Username is required');

  validationErrors.value = errors;
};

// Watch form changes for validation
watch([selectedBrands, selectedUrls, selectedBrowsers, selectedViewports, selectedDrivers, selectedEnvironments, username], validateForm, { deep: true });

// Actions
const handleSubmit = async () => {
  validateForm();
  
  if (!isFormValid.value) {
    appStore.showErrorNotification('Validation Error', 'Please fix the form errors before submitting');
    return;
  }

  // Update test run config
  testRunStore.updateConfig({
    brands: selectedBrands.value,
    urls: selectedUrls.value,
    browsers: selectedBrowsers.value,
    viewports: selectedViewports.value,
    drivers: selectedDrivers.value,
    environments: selectedEnvironments.value,
    username: username.value.trim(),
    maxConcurrency: maxConcurrency.value,
  });

  // Start the test
  const result = await testRunStore.runTest();
  
  if (result.success) {
    appStore.showSuccessNotification('Test Started', 'Test execution has begun successfully');
  } else {
    appStore.showErrorNotification('Test Failed', result.error || 'Failed to start test');
  }
};

const stopTest = async () => {
  const result = await testRunStore.stopTest();
  
  if (result.success) {
    appStore.showWarningNotification('Test Stopped', 'Test execution was stopped');
  } else {
    appStore.showErrorNotification('Stop Failed', result.error || 'Failed to stop test');
  }
};

const clearLogs = () => {
  testRunStore.clearLogs();
};

// Utility functions
const formatTime = (milliseconds: number): string => {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleString();
};

// Auto-scroll logs
watch(() => testRunStore.state.logs.length, async () => {
  await nextTick();
  if (logsContainer.value) {
    logsContainer.value.scrollTop = logsContainer.value.scrollHeight;
  }
});

// Load saved username from localStorage
onMounted(() => {
  const savedUsername = localStorage.getItem('oracle-username');
  if (savedUsername) {
    username.value = savedUsername;
  }
});

// Save username to localStorage
watch(username, (newUsername) => {
  if (newUsername.trim()) {
    localStorage.setItem('oracle-username', newUsername.trim());
  }
});
</script>

<style scoped lang="scss">
.test-runner {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  padding: 1rem;
  max-width: 1400px;
  margin: 0 auto;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
}

.test-config-section {
  background: var(--color-background, #ffffff);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  height: fit-content;

  h2 {
    margin: 0 0 1.5rem 0;
    color: var(--color-text-primary, #1f2937);
    font-size: 1.5rem;
    font-weight: 600;
  }
}

.test-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-weight: 600;
  color: var(--color-text-primary, #1f2937);
  font-size: 0.875rem;
}

.checkbox-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
  max-height: 150px;
  overflow-y: auto;
  padding: 0.5rem;
  border: 1px solid var(--color-border, #d1d5db);
  border-radius: 0.375rem;
  background: var(--color-background-secondary, #f9fafb);
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem;
  cursor: pointer;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--color-background, #ffffff);
  }

  input[type="checkbox"] {
    margin: 0;
  }

  span {
    font-size: 0.875rem;
    color: var(--color-text-secondary, #6b7280);
  }
}

.form-input {
  padding: 0.75rem;
  border: 1px solid var(--color-border, #d1d5db);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;

  &:focus {
    outline: none;
    border-color: var(--color-primary, #3b82f6);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
}

.advanced-options {
  border: 1px solid var(--color-border, #d1d5db);
  border-radius: 0.375rem;
  padding: 1rem;

  summary {
    cursor: pointer;
    font-weight: 600;
    color: var(--color-text-primary, #1f2937);
    margin-bottom: 1rem;
  }

  .form-group {
    margin-top: 1rem;
  }
}

.validation-errors {
  background-color: var(--color-danger-light, #fef2f2);
  border: 1px solid var(--color-danger, #ef4444);
  border-radius: 0.375rem;
  padding: 1rem;

  h4 {
    margin: 0 0 0.5rem 0;
    color: var(--color-danger, #ef4444);
    font-size: 0.875rem;
  }

  ul {
    margin: 0;
    padding-left: 1.5rem;
    color: var(--color-danger, #ef4444);
    font-size: 0.875rem;
  }
}

.estimated-time {
  background-color: var(--color-primary-light, rgba(59, 130, 246, 0.1));
  border: 1px solid var(--color-primary, #3b82f6);
  border-radius: 0.375rem;
  padding: 1rem;
  text-align: center;
  color: var(--color-primary, #3b82f6);
}

.form-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.test-monitoring-section {
  background: var(--color-background, #ffffff);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  height: fit-content;

  h3 {
    margin: 0 0 1.5rem 0;
    color: var(--color-text-primary, #1f2937);
    font-size: 1.25rem;
    font-weight: 600;
  }
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;

  h3 {
    margin: 0;
  }
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;

  &.connected {
    background-color: var(--color-success-light, #f0fdf4);
    color: var(--color-success, #10b981);

    .status-indicator {
      background-color: var(--color-success, #10b981);
    }
  }

  &.connecting {
    background-color: var(--color-warning-light, #fffbeb);
    color: var(--color-warning, #f59e0b);

    .status-indicator {
      background-color: var(--color-warning, #f59e0b);
      animation: pulse 1.5s ease-in-out infinite;
    }
  }

  &.disconnected {
    background-color: var(--color-danger-light, #fef2f2);
    color: var(--color-danger, #ef4444);

    .status-indicator {
      background-color: var(--color-danger, #ef4444);
    }
  }
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-text {
  text-transform: capitalize;
}

.progress-section {
  margin-bottom: 2rem;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.progress-label {
  font-size: 0.875rem;
  color: var(--color-text-secondary, #6b7280);
}

.progress-percentage {
  font-weight: 600;
  color: var(--color-primary, #3b82f6);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--color-border-light, #e5e7eb);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background-color: var(--color-primary, #3b82f6);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: var(--color-text-tertiary, #9ca3af);
}

.logs-section {
  margin-bottom: 2rem;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;

  h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--color-text-primary, #1f2937);
  }
}

.logs-container {
  background-color: var(--color-background-dark, #1f2937);
  color: var(--color-text-light, #f9fafb);
  border-radius: 0.375rem;
  padding: 1rem;
  height: 300px;
  overflow-y: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  line-height: 1.4;
}

.log-entry {
  margin-bottom: 0.25rem;
  word-break: break-all;
}

.no-logs {
  color: var(--color-text-tertiary, #9ca3af);
  font-style: italic;
  text-align: center;
  padding: 2rem;
}

.results-section {
  h4 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--color-text-primary, #1f2937);
  }
}

.result-card {
  border-radius: 0.375rem;
  padding: 1rem;
  border: 1px solid;

  &.success {
    background-color: var(--color-success-light, #f0fdf4);
    border-color: var(--color-success, #10b981);
  }

  &.error {
    background-color: var(--color-danger-light, #fef2f2);
    border-color: var(--color-danger, #ef4444);
  }
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.result-status {
  font-weight: 600;

  .success & {
    color: var(--color-success, #10b981);
  }

  .error & {
    color: var(--color-danger, #ef4444);
  }
}

.result-date {
  font-size: 0.875rem;
  color: var(--color-text-secondary, #6b7280);
}

.result-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.result-item {
  font-size: 0.875rem;
  color: var(--color-text-secondary, #6b7280);

  &.error {
    color: var(--color-danger, #ef4444);
  }
}
</style>
