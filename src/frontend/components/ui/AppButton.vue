<template>
  <button
    :class="buttonClasses"
    :disabled="disabled || loading"
    :type="type"
    @click="handleClick"
  >
    <span v-if="loading" class="loading-spinner"></span>
    <slot v-if="!loading" name="icon"></slot>
    <span v-if="!loading || showTextWhileLoading" class="button-text">
      <slot></slot>
    </span>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  showTextWhileLoading?: boolean;
  type?: 'button' | 'submit' | 'reset';
  fullWidth?: boolean;
}

interface Emits {
  (e: 'click', event: MouseEvent): void;
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  disabled: false,
  loading: false,
  showTextWhileLoading: false,
  type: 'button',
  fullWidth: false,
});

const emit = defineEmits<Emits>();

const buttonClasses = computed(() => [
  'app-button',
  `app-button--${props.variant}`,
  `app-button--${props.size}`,
  {
    'app-button--disabled': props.disabled,
    'app-button--loading': props.loading,
    'app-button--full-width': props.fullWidth,
  },
]);

const handleClick = (event: MouseEvent): void => {
  if (!props.disabled && !props.loading) {
    emit('click', event);
  }
};
</script>

<style scoped lang="scss">
.app-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  position: relative;
  white-space: nowrap;

  &:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px var(--color-primary-light, #3b82f6);
  }

  // Sizes
  &--sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  &--md {
    padding: 0.625rem 1rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  &--lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    line-height: 1.5rem;
  }

  // Variants
  &--primary {
    background-color: var(--color-primary, #3b82f6);
    color: white;

    &:hover:not(.app-button--disabled):not(.app-button--loading) {
      background-color: var(--color-primary-dark, #2563eb);
    }

    &:active:not(.app-button--disabled):not(.app-button--loading) {
      background-color: var(--color-primary-darker, #1d4ed8);
    }
  }

  &--secondary {
    background-color: var(--color-secondary, #6b7280);
    color: white;

    &:hover:not(.app-button--disabled):not(.app-button--loading) {
      background-color: var(--color-secondary-dark, #4b5563);
    }
  }

  &--success {
    background-color: var(--color-success, #10b981);
    color: white;

    &:hover:not(.app-button--disabled):not(.app-button--loading) {
      background-color: var(--color-success-dark, #059669);
    }
  }

  &--warning {
    background-color: var(--color-warning, #f59e0b);
    color: white;

    &:hover:not(.app-button--disabled):not(.app-button--loading) {
      background-color: var(--color-warning-dark, #d97706);
    }
  }

  &--danger {
    background-color: var(--color-danger, #ef4444);
    color: white;

    &:hover:not(.app-button--disabled):not(.app-button--loading) {
      background-color: var(--color-danger-dark, #dc2626);
    }
  }

  &--ghost {
    background-color: transparent;
    color: var(--color-text-primary, #1f2937);
    border: 1px solid var(--color-border, #d1d5db);

    &:hover:not(.app-button--disabled):not(.app-button--loading) {
      background-color: var(--color-background-secondary, #f9fafb);
    }
  }

  // States
  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &--loading {
    cursor: wait;
  }

  &--full-width {
    width: 100%;
  }

  .loading-spinner {
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .button-text {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
}
</style>
