<template>
  <Teleport to="body">
    <div v-if="notifications.length > 0" class="notifications-container">
      <TransitionGroup name="notification" tag="div" class="notifications-list">
        <div
          v-for="notification in notifications"
          :key="notification.id"
          :class="getNotificationClasses(notification)"
          @click="handleNotificationClick(notification)"
        >
          <!-- Icon -->
          <div class="notification-icon">
            <component :is="getIconComponent(notification.type)" />
          </div>

          <!-- Content -->
          <div class="notification-content">
            <h4 class="notification-title">{{ notification.title }}</h4>
            <p class="notification-message">{{ notification.message }}</p>
            
            <!-- Actions -->
            <div v-if="notification.actions && notification.actions.length > 0" class="notification-actions">
              <button
                v-for="action in notification.actions"
                :key="action.label"
                class="notification-action"
                @click.stop="action.action"
              >
                {{ action.label }}
              </button>
            </div>
          </div>

          <!-- Close button -->
          <button
            class="notification-close"
            @click.stop="removeNotification(notification.id)"
          >
            <CloseIcon />
          </button>
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useAppStore } from '@/frontend/stores/modules/appStore.ts';
import type { Notification } from '@/frontend/stores/types/index.ts';

// Icons (you would import these from your icon library)
const CheckCircleIcon = () => '✓';
const ExclamationTriangleIcon = () => '⚠';
const XCircleIcon = () => '✕';
const InformationCircleIcon = () => 'ℹ';
const CloseIcon = () => '×';

const appStore = useAppStore();

const notifications = computed(() => appStore.notifications.notifications);

const getNotificationClasses = (notification: Notification) => [
  'notification',
  `notification--${notification.type}`,
];

const getIconComponent = (type: Notification['type']) => {
  switch (type) {
    case 'success':
      return CheckCircleIcon;
    case 'warning':
      return ExclamationTriangleIcon;
    case 'error':
      return XCircleIcon;
    case 'info':
    default:
      return InformationCircleIcon;
  }
};

const handleNotificationClick = (notification: Notification) => {
  // If notification has only one action, execute it
  if (notification.actions && notification.actions.length === 1) {
    notification.actions[0].action();
  }
};

const removeNotification = (id: string) => {
  appStore.removeNotification(id);
};
</script>

<style scoped lang="scss">
.notifications-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1050;
  max-width: 400px;
  width: 100%;
}

.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.notification {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background-color: var(--color-background, #ffffff);
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-left: 4px solid;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  &--success {
    border-left-color: var(--color-success, #10b981);

    .notification-icon {
      color: var(--color-success, #10b981);
    }
  }

  &--error {
    border-left-color: var(--color-danger, #ef4444);

    .notification-icon {
      color: var(--color-danger, #ef4444);
    }
  }

  &--warning {
    border-left-color: var(--color-warning, #f59e0b);

    .notification-icon {
      color: var(--color-warning, #f59e0b);
    }
  }

  &--info {
    border-left-color: var(--color-primary, #3b82f6);

    .notification-icon {
      color: var(--color-primary, #3b82f6);
    }
  }
}

.notification-icon {
  flex-shrink: 0;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  font-weight: bold;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-text-primary, #1f2937);
}

.notification-message {
  margin: 0;
  font-size: 0.875rem;
  color: var(--color-text-secondary, #6b7280);
  line-height: 1.4;
}

.notification-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.notification-action {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: var(--color-primary, #3b82f6);
  color: white;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--color-primary-dark, #2563eb);
  }
}

.notification-close {
  flex-shrink: 0;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: var(--color-text-tertiary, #9ca3af);
  cursor: pointer;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
  font-size: 1.25rem;
  line-height: 1;

  &:hover {
    background-color: var(--color-background-secondary, #f9fafb);
    color: var(--color-text-secondary, #6b7280);
  }
}

// Transitions
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}

// Responsive
@media (max-width: 640px) {
  .notifications-container {
    top: 0.5rem;
    right: 0.5rem;
    left: 0.5rem;
    max-width: none;
  }

  .notification {
    padding: 0.75rem;
  }

  .notification-title {
    font-size: 0.8125rem;
  }

  .notification-message {
    font-size: 0.8125rem;
  }
}
</style>
