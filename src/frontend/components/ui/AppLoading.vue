<template>
  <div :class="loadingClasses">
    <div class="loading-content">
      <!-- Spinner -->
      <div v-if="type === 'spinner'" class="spinner">
        <div class="spinner-ring"></div>
      </div>

      <!-- Dots -->
      <div v-else-if="type === 'dots'" class="dots">
        <div class="dot"></div>
        <div class="dot"></div>
        <div class="dot"></div>
      </div>

      <!-- Pulse -->
      <div v-else-if="type === 'pulse'" class="pulse">
        <div class="pulse-circle"></div>
      </div>

      <!-- Progress bar -->
      <div v-else-if="type === 'progress'" class="progress">
        <div class="progress-bar" :style="{ width: `${progress}%` }"></div>
      </div>

      <!-- Text content -->
      <div v-if="message || $slots.default" class="loading-text">
        <h3 v-if="message" class="loading-message">{{ message }}</h3>
        <slot></slot>
        <p v-if="description" class="loading-description">{{ description }}</p>
      </div>

      <!-- Progress info -->
      <div v-if="showProgress && (progress !== undefined || estimatedTime)" class="progress-info">
        <div v-if="progress !== undefined" class="progress-percentage">
          {{ Math.round(progress) }}%
        </div>
        <div v-if="estimatedTime" class="estimated-time">
          Estimated time: {{ formatTime(estimatedTime) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  type?: 'spinner' | 'dots' | 'pulse' | 'progress';
  size?: 'sm' | 'md' | 'lg';
  message?: string;
  description?: string;
  progress?: number;
  estimatedTime?: number; // in milliseconds
  showProgress?: boolean;
  overlay?: boolean;
  fullscreen?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'spinner',
  size: 'md',
  showProgress: false,
  overlay: false,
  fullscreen: false,
});

const loadingClasses = computed(() => [
  'app-loading',
  `app-loading--${props.size}`,
  {
    'app-loading--overlay': props.overlay,
    'app-loading--fullscreen': props.fullscreen,
  },
]);

const formatTime = (milliseconds: number): string => {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
};
</script>

<style scoped lang="scss">
.app-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;

  &--overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
  }

  &--fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--color-background, #ffffff);
    z-index: 999;
  }

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    text-align: center;
    max-width: 400px;
  }

  // Sizes
  &--sm {
    .spinner-ring {
      width: 24px;
      height: 24px;
    }

    .dot {
      width: 6px;
      height: 6px;
    }

    .pulse-circle {
      width: 24px;
      height: 24px;
    }

    .loading-message {
      font-size: 0.875rem;
    }
  }

  &--md {
    .spinner-ring {
      width: 32px;
      height: 32px;
    }

    .dot {
      width: 8px;
      height: 8px;
    }

    .pulse-circle {
      width: 32px;
      height: 32px;
    }

    .loading-message {
      font-size: 1rem;
    }
  }

  &--lg {
    .spinner-ring {
      width: 48px;
      height: 48px;
    }

    .dot {
      width: 12px;
      height: 12px;
    }

    .pulse-circle {
      width: 48px;
      height: 48px;
    }

    .loading-message {
      font-size: 1.25rem;
    }
  }

  // Spinner
  .spinner {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .spinner-ring {
    border: 3px solid var(--color-border-light, #e5e7eb);
    border-top: 3px solid var(--color-primary, #3b82f6);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  // Dots
  .dots {
    display: flex;
    gap: 0.5rem;
  }

  .dot {
    background-color: var(--color-primary, #3b82f6);
    border-radius: 50%;
    animation: bounce 1.4s ease-in-out infinite both;

    &:nth-child(1) {
      animation-delay: -0.32s;
    }

    &:nth-child(2) {
      animation-delay: -0.16s;
    }
  }

  // Pulse
  .pulse {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .pulse-circle {
    background-color: var(--color-primary, #3b82f6);
    border-radius: 50%;
    animation: pulse 1.5s ease-in-out infinite;
  }

  // Progress
  .progress {
    width: 200px;
    height: 4px;
    background-color: var(--color-border-light, #e5e7eb);
    border-radius: 2px;
    overflow: hidden;
  }

  .progress-bar {
    height: 100%;
    background-color: var(--color-primary, #3b82f6);
    border-radius: 2px;
    transition: width 0.3s ease;
  }

  // Text
  .loading-text {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .loading-message {
    margin: 0;
    font-weight: 600;
    color: var(--color-text-primary, #1f2937);
  }

  .loading-description {
    margin: 0;
    font-size: 0.875rem;
    color: var(--color-text-secondary, #6b7280);
  }

  // Progress info
  .progress-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    font-size: 0.875rem;
    color: var(--color-text-secondary, #6b7280);
  }

  .progress-percentage {
    font-weight: 600;
    color: var(--color-primary, #3b82f6);
  }

  // Animations
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes bounce {
    0%, 80%, 100% {
      transform: scale(0);
    }
    40% {
      transform: scale(1);
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(0);
      opacity: 1;
    }
    100% {
      transform: scale(1);
      opacity: 0;
    }
  }
}
</style>
