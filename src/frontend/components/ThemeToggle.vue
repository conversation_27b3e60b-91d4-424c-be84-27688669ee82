<template>
    <button id="theme-toggle" @click="toggleTheme">
        <span v-if="theme === 'light'" class="icon dark-icon">&#9789;</span>
        <span v-else class="icon light-icon">&#9788;</span>
    </button>
</template>

<script setup>
import useLocalstorageRef from "@/frontend/customRefs/localstorageRef";
import {watch} from "vue";

const theme = useLocalstorageRef('oracle--theme', 'dark');

const toggleTheme = () => {
    theme.value = theme.value === 'dark' ? 'light' : 'dark';
};

watch(() => theme, (newValue) => {
    const htmlElement = document.querySelector('html');

    htmlElement.setAttribute('class', '');
    htmlElement.classList.add(`theme--${newValue.value}`);
}, {
    deep: true,
    immediate: true,
});
</script>

<style lang="scss" scoped>
#theme-toggle {
    background: none;
    height: 2.5rem;
    width: 2.5rem;
    border: none;
    color: var(--color-text-primary);
    cursor: pointer;
    padding: 0;
    border-radius: var(--border-radius-full);
    transition: all var(--transition-fast);
    position: relative;
    display: grid;
    place-items: center;
    overflow: hidden;

    &:hover {
        background-color: var(--color-background-tertiary);
        transform: rotate(15deg);
    }

    &:focus {
        outline: none;
        box-shadow: 0 0 0 2px var(--color-primary-light);
    }

    &:active {
        transform: scale(0.95);
    }

    .icon {
        font-size: var(--font-size-3xl);
        height: 100%;
        width: 100%;
        transition: all var(--transition-normal);

        &.light-icon {
            color: var(--color-warning);
        }

        &.dark-icon {
            color: var(--color-text-secondary);
        }
    }
}
</style>