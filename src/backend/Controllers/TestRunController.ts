import {Request, Response} from 'express';
import {exec} from 'child_process';
import {promisify} from 'util';

const execPromise = promisify(exec);

/**
 * Controller for handling test run requests
 */
export default class TestRunController {
    /**
     * Run a frontend test based on the provided parameters
     */
    public static async runTest(req: Request, res: Response): Promise<void> {
        try {
            const {
                brands,
                drivers,
                viewports,
                browsers,
                urls,
                headless,
                audit,
                debug,
                force,
                username,
            } = req.body;

            // Validate required parameters
            if (!brands || !drivers || !viewports || !browsers || !urls) {
                res.status(400).json({
                    success: false,
                    error: 'Missing required parameters',
                    output: '',
                });
                return;
            }

            // Build the command
            const parts = [
                `--brands=${Array.isArray(brands) ? brands.join(',') : brands}`,
                `--drivers=${Array.isArray(drivers) ? drivers.join(',') : drivers}`,
                `--viewports=${Array.isArray(viewports) ? viewports.join(',') : viewports}`,
                `--browsers=${Array.isArray(browsers) ? browsers.join(',') : browsers}`,
                `--urls=${Array.isArray(urls) ? urls.join(',') : urls}`,
            ];

            // Add username if provided
            if (username) {
                parts.push(`--username=${username}`);
            }

            if (headless) {
                parts.push('--headless');
            }

            if (audit) {
                parts.push('--audit');
            }

            if (debug) {
                parts.push('--debug');
            }

            if (force) {
                parts.push('--force');
            }

            const command = `yarn test:frontend ${parts.join(' ')}`;

            console.log(`Executing command: ${command}`);

            // Execute the command
            const {stdout, stderr} = await execPromise(command);

            // Check for errors
            if (stderr && !stderr.includes('warning')) {
                res.status(500).json({
                    success: false,
                    error: 'Error executing test command',
                    output: stderr,
                });
                return;
            }

            // Return success
            res.status(200).json({
                success: true,
                output: stdout,
            });
        } catch (error) {
            console.error('Error running test:', error);

            res.status(500).json({
                success: false,
                error: error.message,
                output: error.stderr || '',
            });
        }
    }
}
