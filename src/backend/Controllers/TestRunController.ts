import {Request, Response} from 'express';
import TestService from '@/backend/Services/TestService.ts';
import OracleConfig from '@/shared/types/Common/OracleConfig.ts';
import Brand from '@/backend/Common/Brand.ts';
import Log from '@/backend/Common/Log.ts';

interface TestRunRequest {
    brands: string[];
    drivers: string[];
    viewports: string[];
    browsers: string[];
    urls: string[];
    username?: string;
    environments?: string[];
    maxConcurrency?: number;
}

interface TestRunResponse {
    success: boolean;
    data?: {
        date: string;
        totalTestCases: number;
        executionTime: number;
        estimatedTime?: number;
    };
    error?: string;
    validationErrors?: string[];
}

/**
 * Controller for handling test run requests
 * Rewritten to use the new TestService architecture
 */
export default class TestRunController {
    private static testService = new TestService();

    /**
     * Run a frontend test based on the provided parameters
     */
    public static async runTest(req: Request, res: Response): Promise<void> {
        try {
            const requestData = req.body as TestRunRequest;

            // Validate request data
            const validation = TestRunController.validateRequest(requestData);
            if (!validation.valid) {
                res.status(400).json({
                    success: false,
                    error: 'Invalid request parameters',
                    validationErrors: validation.errors,
                } as TestRunResponse);
                return;
            }

            // Check if test service is already running
            if (TestRunController.testService.isRunning()) {
                res.status(409).json({
                    success: false,
                    error: 'Test execution is already in progress',
                } as TestRunResponse);
                return;
            }

            // Build Oracle configuration from request
            const oracleConfig = TestRunController.buildOracleConfig(requestData);

            // Validate Oracle configuration
            const configValidation = TestRunController.testService.validateConfig(oracleConfig);
            if (!configValidation.valid) {
                res.status(400).json({
                    success: false,
                    error: 'Invalid Oracle configuration',
                    validationErrors: configValidation.errors,
                } as TestRunResponse);
                return;
            }

            // Get estimated execution time
            const estimatedTime = TestRunController.testService.estimateExecutionTime(oracleConfig);

            Log.info(`Starting test execution with estimated time: ${estimatedTime}ms`);

            // Execute the test run
            const result = await TestRunController.testService.executeTests(oracleConfig);

            // Return response
            if (result.success) {
                res.status(200).json({
                    success: true,
                    data: {
                        date: result.date,
                        totalTestCases: result.totalTestCases,
                        executionTime: result.executionTime,
                        estimatedTime,
                    },
                } as TestRunResponse);
            } else {
                res.status(500).json({
                    success: false,
                    error: result.error || 'Test execution failed',
                } as TestRunResponse);
            }
        } catch (error) {
            Log.error(`Error in test run controller: ${error instanceof Error ? error.message : 'Unknown error'}`);

            res.status(500).json({
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
            } as TestRunResponse);
        }
    }

    /**
     * Validate the incoming request data
     */
    private static validateRequest(data: TestRunRequest): { valid: boolean; errors: string[] } {
        const errors: string[] = [];

        if (!data.brands || !Array.isArray(data.brands) || data.brands.length === 0) {
            errors.push('At least one brand must be specified');
        }

        if (!data.drivers || !Array.isArray(data.drivers) || data.drivers.length === 0) {
            errors.push('At least one driver must be specified');
        }

        if (!data.viewports || !Array.isArray(data.viewports) || data.viewports.length === 0) {
            errors.push('At least one viewport must be specified');
        }

        if (!data.browsers || !Array.isArray(data.browsers) || data.browsers.length === 0) {
            errors.push('At least one browser must be specified');
        }

        if (!data.urls || !Array.isArray(data.urls) || data.urls.length === 0) {
            errors.push('At least one URL must be specified');
        }

        return {
            valid: errors.length === 0,
            errors,
        };
    }

    /**
     * Build Oracle configuration from request data
     */
    private static buildOracleConfig(data: TestRunRequest): OracleConfig {
        try {
            // Load configuration from shared config files
            const brands = require('@/shared/config/brands/brands.json');
            const browsers = require('@/shared/config/browsers.ts');
            const viewports = require('@/shared/config/viewports.ts');

            // Build brand objects
            const brandObjects = data.brands.map(slug => {
                const brandConfig = brands.brands.find((b: any) => b.slug === slug);
                if (!brandConfig) {
                    throw new Error(`Brand not found: ${slug}`);
                }
                return new Brand(brandConfig);
            });

            // Build browser objects
            const browserObjects = data.browsers.map(product => {
                const browserConfig = browsers.find((b: any) => b.product === product);
                if (!browserConfig) {
                    throw new Error(`Browser not found: ${product}`);
                }
                return {
                    product: browserConfig.product,
                    displayName: browserConfig.displayName,
                    executable: browserConfig.executable,
                };
            });

            // Build viewport objects
            const viewportObjects = data.viewports.map(name => {
                const viewportConfig = viewports.find((v: any) => v.name === name);
                if (!viewportConfig) {
                    throw new Error(`Viewport not found: ${name}`);
                }
                return {
                    name: viewportConfig.name,
                    width: viewportConfig.width,
                    height: viewportConfig.height,
                };
            });

            return {
                brands: brandObjects,
                urls: data.urls,
                browsers: browserObjects,
                viewports: viewportObjects,
                drivers: data.drivers,
                user: data.username || 'api-user',
            } as OracleConfig;
        } catch (error) {
            throw new Error(`Failed to build Oracle config: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Get the current status of the test service
     */
    public static async getStatus(req: Request, res: Response): Promise<void> {
        try {
            const status = TestRunController.testService.getStatus();
            const config = TestRunController.testService.getConfig();

            res.status(200).json({
                success: true,
                data: {
                    status,
                    config,
                },
            });
        } catch (error) {
            Log.error(`Error getting test service status: ${error instanceof Error ? error.message : 'Unknown error'}`);

            res.status(500).json({
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
            });
        }
    }
}
