class Brand {
    private brandConfig: any;

    constructor(brandConfig: any) {
        this.brandConfig = brandConfig;
    }

    get slug(): string {
        return this.brandConfig.slug;
    }

    get domains(): [{ host: string, locale: string }] {
        return this.brandConfig.domains;
    }

    getSubDomainByPrefix(prefix: string): string {
        const fallbackSubDomain = this.getSubDomain();

        return this.domains.find((domain: { host: string }) => domain.host.startsWith(prefix))?.host
            || this.domains.find((domain: { host: string }) => domain.host.startsWith(fallbackSubDomain))?.host
            || this.domains.find((domain: { host: string }) => domain.host.startsWith('www'))?.host
            || this.domains.at(0)?.host;
    }

    getSubDomain(): string {
        if (this.brandConfig.google_adsense.contract_type === 'online') {
            return 'search';
        }

        return 'www';
    }
}

export default Brand;
