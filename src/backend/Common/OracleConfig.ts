import * as fs from 'node:fs';
import BrandWebsitesBrandsReader from '../Test/BrandWebsitesBrandsReader.ts';
import yargs from 'yargs';
import {hideBin} from 'yargs/helpers';
import viewports from '@/shared/config/viewports.ts';
import {forEach, mergeWith} from 'lodash-es';
import browsers from '@/shared/config/browsers.ts';
import type {default as OracleConfigType} from '@/shared/types/Common/OracleConfig.ts';
import Brand from './Brand.ts';

class OracleConfig {
    config: OracleConfigType = {
        brands: [],
        browsers: [],
        randomBrandCount: 3,
        urls: [],
        drivers: [],
        user: '',
        viewports: [],
    };
    loaded = false;

    async readConfig(): Promise<this> {
        const configPath = `${process.env.HOME}/oracle.config.json`;

        if (!fs.existsSync(configPath)) {
            return this;
        }

        const config = fs.readFileSync(configPath, 'utf8');

        if (!config) {
            return this;
        }

        this.config = JSON.parse(config);
        this.loaded = true;

        return this;
    }

    mergeConfig(additionalConfig: any) {
        this.config = mergeWith(
            additionalConfig,
            this.config,
            (objValue, srcValue) => {
                if (Array.isArray(objValue)) {
                    return objValue.concat(srcValue);
                }

                return objValue;
            },
        );

        return this;
    }

    setDefaults() {
        const parameterList = [
            'brands',
            'viewports',
            'browsers',
        ];
        const parameterListDefaults = {
            brands: this.config.brands?.length
                ? BrandWebsitesBrandsReader.getBrands(this.config.brands as unknown as string[])
                : BrandWebsitesBrandsReader.getRandomBrands(
                    this.config.randomBrandCount,
                ),
            browsers: ['chrome'],
            viewports: ['desktop'],
        };
        const usedArguments = yargs(hideBin(process.argv)).argv;
        forEach(parameterList, parameter => {
            const currentValue = this.config[parameter]?.length ? this.config[parameter] : null;

            this.config[parameter] = usedArguments[parameter]?.split(',') || currentValue || parameterListDefaults[parameter] || null;
        });
        this.config.brands = this.config.brands?.map((brand: Brand | string): Brand => {
            if (typeof brand === 'string') {
                brand = BrandWebsitesBrandsReader.getBySlug(brand);
            }

            return brand;
        });
        this.config.viewports = this.config.viewports?.length
            ? viewports.filter(viewport => (this.config.viewports as unknown as string[])
                .includes(viewport.name))
            || viewports
            : viewports;
        this.config.browsers = this.config.browsers?.length
            ? browsers.filter(browser => (this.config.browsers as unknown as string[])
                .includes(browser.product))
            || browsers
            : browsers;

        return this;
    }
}

export default OracleConfig;
