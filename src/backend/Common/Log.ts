import * as fs from 'node:fs';
import * as path from 'node:path';

class Log {
    static logDir = path.resolve('public', 'logs');
    static logFileName = `log-${new Date().toISOString().split('T')[0]}.log`;
    static logFilePath = path.join(Log.logDir, Log.logFileName);

    static init() {
        if (!fs.existsSync(Log.logDir)) {
            fs.mkdirSync(Log.logDir, {recursive: true});
            console.log(`Created logs directory at: ${Log.logDir}`);
        }
    }

    static log(message: string, level = 'INFO') {
        if (typeof message === 'object' || Array.isArray(message)) {
            message = JSON.stringify(message, null, 2);
        }

        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] [${level}] ${message}\n`;

        console.log(logMessage.trim());

        fs.appendFileSync(Log.logFilePath, logMessage);
    }

    static info(message: string) {
        Log.log(message, 'INFO');
    }

    static warn(message: string) {
        Log.log(message, 'WARN');
    }

    static error(message: string) {
        Log.log(message, 'ERROR');
    }

    static debug(message: string) {
        Log.log(message, 'DEBUG');
    }

    static clearLogs() {
        try {
            fs.rmdirSync(Log.logDir, {recursive: true});
            console.log(`Logs cleared at: ${Log.logDir}`);
        } catch (err) {
            console.error(`Error clearing logs: ${err.message}`);
        }
    }
}

// Initialize the Log class
Log.init();

export default Log;

