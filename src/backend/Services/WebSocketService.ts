import { WebSocketServer, WebSocket } from 'ws';
import { Server } from 'http';
import Log from '@/backend/Common/Log.ts';

interface WebSocketMessage {
  type: 'test-progress' | 'test-log' | 'test-status' | 'test-result';
  data: any;
  timestamp: string;
}

interface ConnectedClient {
  id: string;
  ws: WebSocket;
  subscriptions: Set<string>;
}

/**
 * WebSocket service for real-time communication with frontend
 */
class WebSocketService {
  private wss: WebSocketServer | null = null;
  private clients: Map<string, ConnectedClient> = new Map();
  private messageQueue: WebSocketMessage[] = [];
  private maxQueueSize = 1000;

  /**
   * Initialize WebSocket server
   */
  public initialize(server: Server): void {
    try {
      this.wss = new WebSocketServer({ 
        server,
        path: '/ws',
        perMessageDeflate: false,
      });

      this.wss.on('connection', this.handleConnection.bind(this));
      this.wss.on('error', this.handleError.bind(this));

      Log.info('WebSocket server initialized on /ws');
    } catch (error) {
      Log.error(`Failed to initialize WebSocket server: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }

  /**
   * Handle new WebSocket connection
   */
  private handleConnection(ws: WebSocket, request: any): void {
    const clientId = this.generateClientId();
    const client: ConnectedClient = {
      id: clientId,
      ws,
      subscriptions: new Set(['test-progress', 'test-log', 'test-status', 'test-result']),
    };

    this.clients.set(clientId, client);
    Log.info(`WebSocket client connected: ${clientId}`);

    // Send welcome message
    this.sendToClient(clientId, {
      type: 'test-status',
      data: { 
        message: 'Connected to Oracle WebSocket',
        clientId,
        timestamp: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    });

    // Send queued messages to new client
    this.sendQueuedMessages(clientId);

    // Handle client messages
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleClientMessage(clientId, message);
      } catch (error) {
        Log.warn(`Invalid message from client ${clientId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    });

    // Handle client disconnect
    ws.on('close', () => {
      this.clients.delete(clientId);
      Log.info(`WebSocket client disconnected: ${clientId}`);
    });

    // Handle client errors
    ws.on('error', (error) => {
      Log.error(`WebSocket client error ${clientId}:`, error);
      this.clients.delete(clientId);
    });
  }

  /**
   * Handle WebSocket server errors
   */
  private handleError(error: Error): void {
    Log.error('WebSocket server error:', error);
  }

  /**
   * Handle messages from clients
   */
  private handleClientMessage(clientId: string, message: any): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    switch (message.type) {
      case 'subscribe':
        if (Array.isArray(message.channels)) {
          message.channels.forEach((channel: string) => {
            client.subscriptions.add(channel);
          });
        }
        break;

      case 'unsubscribe':
        if (Array.isArray(message.channels)) {
          message.channels.forEach((channel: string) => {
            client.subscriptions.delete(channel);
          });
        }
        break;

      case 'ping':
        this.sendToClient(clientId, {
          type: 'test-status',
          data: { message: 'pong' },
          timestamp: new Date().toISOString(),
        });
        break;

      default:
        Log.warn(`Unknown message type from client ${clientId}:`, message.type);
    }
  }

  /**
   * Send test progress update
   */
  public sendTestProgress(progress: number, currentStep: string, totalSteps?: number): void {
    this.broadcast({
      type: 'test-progress',
      data: {
        progress,
        currentStep,
        totalSteps,
      },
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Send test log message
   */
  public sendTestLog(message: string, level: 'info' | 'warn' | 'error' = 'info'): void {
    this.broadcast({
      type: 'test-log',
      data: {
        message,
        level,
      },
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Send test status update
   */
  public sendTestStatus(status: 'starting' | 'running' | 'completed' | 'failed' | 'stopped', data?: any): void {
    this.broadcast({
      type: 'test-status',
      data: {
        status,
        ...data,
      },
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Send test result
   */
  public sendTestResult(result: any): void {
    this.broadcast({
      type: 'test-result',
      data: result,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Broadcast message to all connected clients
   */
  private broadcast(message: WebSocketMessage): void {
    // Add to queue for new clients
    this.addToQueue(message);

    // Send to all connected clients
    this.clients.forEach((client) => {
      if (client.subscriptions.has(message.type)) {
        this.sendToClient(client.id, message);
      }
    });
  }

  /**
   * Send message to specific client
   */
  private sendToClient(clientId: string, message: WebSocketMessage): void {
    const client = this.clients.get(clientId);
    if (!client || client.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    try {
      client.ws.send(JSON.stringify(message));
    } catch (error) {
      Log.error(`Failed to send message to client ${clientId}:`, error);
      this.clients.delete(clientId);
    }
  }

  /**
   * Add message to queue for new clients
   */
  private addToQueue(message: WebSocketMessage): void {
    this.messageQueue.push(message);

    // Keep queue size manageable
    if (this.messageQueue.length > this.maxQueueSize) {
      this.messageQueue = this.messageQueue.slice(-this.maxQueueSize);
    }
  }

  /**
   * Send queued messages to new client
   */
  private sendQueuedMessages(clientId: string): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    // Send last 50 messages
    const recentMessages = this.messageQueue.slice(-50);
    recentMessages.forEach((message) => {
      if (client.subscriptions.has(message.type)) {
        this.sendToClient(clientId, message);
      }
    });
  }

  /**
   * Generate unique client ID
   */
  private generateClientId(): string {
    return `client-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get connected clients count
   */
  public getConnectedClientsCount(): number {
    return this.clients.size;
  }

  /**
   * Clear message queue
   */
  public clearQueue(): void {
    this.messageQueue = [];
  }

  /**
   * Close all connections and cleanup
   */
  public close(): void {
    if (this.wss) {
      this.wss.close();
      this.wss = null;
    }
    this.clients.clear();
    this.messageQueue = [];
    Log.info('WebSocket service closed');
  }
}

// Export singleton instance
export default new WebSocketService();
