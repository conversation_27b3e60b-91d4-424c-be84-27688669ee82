import FrontendTestRunner from '@/backend/Test/FrontendTestRunner.ts';
import OracleConfig from '@/shared/types/Common/OracleConfig.ts';
import Log from '@/backend/Common/Log.ts';
import WebSocketService from './WebSocketService.ts';

interface TestServiceConfig {
    environments?: string[];
    maxConcurrency?: number;
    cleanupAfterRun?: boolean;
}

interface TestExecutionResult {
    success: boolean;
    date: string;
    totalTestCases: number;
    executionTime: number;
    error?: string;
}

interface TestServiceStatus {
    isRunning: boolean;
    currentRun?: {
        date: string;
        startTime: number;
        totalTestCases: number;
    };
}

/**
 * Service layer for managing test execution
 * Provides a clean interface for running tests and managing test state
 */
class TestService {
    private readonly config: Required<TestServiceConfig>;
    private currentRunner: FrontendTestRunner | null = null;
    private status: TestServiceStatus = { isRunning: false };

    constructor(config: TestServiceConfig = {}) {
        this.config = {
            environments: config.environments || ['production', 'local'],
            maxConcurrency: config.maxConcurrency || 25,
            cleanupAfterRun: config.cleanupAfterRun ?? true,
        };
    }

    /**
     * Execute a test run with the provided Oracle configuration
     */
    public async executeTests(oracleConfig: OracleConfig): Promise<TestExecutionResult> {
        if (this.status.isRunning) {
            throw new Error('Test execution is already in progress');
        }

        if (!oracleConfig) {
            throw new Error('Oracle configuration is required');
        }

        this.status.isRunning = true;
        const startTime = Date.now();

        try {
            Log.info('Starting test execution via TestService');

            // Send WebSocket notification
            WebSocketService.sendTestStatus('starting', {
                estimatedTime: this.estimateExecutionTime(oracleConfig),
                config: oracleConfig,
            });
            WebSocketService.sendTestLog('Starting test execution...', 'info');

            // Create and configure test runner
            this.currentRunner = new FrontendTestRunner(oracleConfig, this.config);

            // Update status with current run info
            this.status.currentRun = {
                date: new Date().toISOString(),
                startTime,
                totalTestCases: 0, // Will be updated after test cases are created
            };

            WebSocketService.sendTestProgress(10, 'Initializing test runner...');

            // Execute the test run
            const result = await this.currentRunner.run();

            // Update total test cases in status
            if (this.status.currentRun) {
                this.status.currentRun.totalTestCases = result.totalTestCases;
            }

            Log.info(`Test execution completed: ${result.success ? 'SUCCESS' : 'FAILED'}`);

            // Send completion notifications
            if (result.success) {
                WebSocketService.sendTestStatus('completed', result);
                WebSocketService.sendTestLog(`Test completed successfully with ${result.totalTestCases} test cases`, 'info');
                WebSocketService.sendTestProgress(100, 'Test completed successfully');
            } else {
                WebSocketService.sendTestStatus('failed', result);
                WebSocketService.sendTestLog(`Test failed: ${result.error}`, 'error');
            }

            WebSocketService.sendTestResult(result);

            return {
                success: result.success,
                date: result.date,
                totalTestCases: result.totalTestCases,
                executionTime: result.executionTime,
                error: result.error,
            };
        } catch (error) {
            const executionTime = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';

            Log.error(`Test execution failed: ${errorMessage}`);

            // Send error notifications
            WebSocketService.sendTestStatus('failed', { error: errorMessage });
            WebSocketService.sendTestLog(`Test execution failed: ${errorMessage}`, 'error');

            return {
                success: false,
                date: new Date().toISOString(),
                totalTestCases: 0,
                executionTime,
                error: errorMessage,
            };
        } finally {
            this.status.isRunning = false;
            this.status.currentRun = undefined;
            this.currentRunner = null;
        }
    }

    /**
     * Get the current status of the test service
     */
    public getStatus(): Readonly<TestServiceStatus> {
        return { ...this.status };
    }

    /**
     * Get the current configuration
     */
    public getConfig(): Readonly<Required<TestServiceConfig>> {
        return { ...this.config };
    }

    /**
     * Check if tests are currently running
     */
    public isRunning(): boolean {
        return this.status.isRunning;
    }

    /**
     * Get information about the current test run (if any)
     */
    public getCurrentRunInfo(): Readonly<TestServiceStatus['currentRun']> | undefined {
        return this.status.currentRun ? { ...this.status.currentRun } : undefined;
    }

    /**
     * Validate Oracle configuration before running tests
     */
    public validateConfig(oracleConfig: OracleConfig): { valid: boolean; errors: string[] } {
        const errors: string[] = [];

        if (!oracleConfig) {
            errors.push('Oracle configuration is required');
            return { valid: false, errors };
        }

        if (!oracleConfig.brands || !Array.isArray(oracleConfig.brands) || oracleConfig.brands.length === 0) {
            errors.push('At least one brand must be configured');
        }

        if (!oracleConfig.urls || !Array.isArray(oracleConfig.urls) || oracleConfig.urls.length === 0) {
            errors.push('At least one URL must be configured');
        }

        if (!oracleConfig.browsers || !Array.isArray(oracleConfig.browsers) || oracleConfig.browsers.length === 0) {
            errors.push('At least one browser must be configured');
        }

        if (!oracleConfig.viewports || !Array.isArray(oracleConfig.viewports) || oracleConfig.viewports.length === 0) {
            errors.push('At least one viewport must be configured');
        }

        if (!oracleConfig.drivers || !Array.isArray(oracleConfig.drivers) || oracleConfig.drivers.length === 0) {
            errors.push('At least one driver must be configured');
        }

        if (!oracleConfig.user || typeof oracleConfig.user !== 'string') {
            errors.push('User must be specified');
        }

        return {
            valid: errors.length === 0,
            errors,
        };
    }

    /**
     * Get estimated execution time based on configuration
     */
    public estimateExecutionTime(oracleConfig: OracleConfig): number {
        if (!oracleConfig) return 0;

        const totalCombinations = 
            (oracleConfig.brands?.length || 0) *
            (oracleConfig.urls?.length || 0) *
            (oracleConfig.browsers?.length || 0) *
            (oracleConfig.viewports?.length || 0) *
            (oracleConfig.drivers?.length || 0) *
            this.config.environments.length;

        // Rough estimate: 5 seconds per test case combination
        return totalCombinations * 5000; // milliseconds
    }
}

export default TestService;
export type { TestServiceConfig, TestExecutionResult, TestServiceStatus };
