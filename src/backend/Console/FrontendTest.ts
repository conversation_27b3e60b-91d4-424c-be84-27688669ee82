import inquirer from 'inquirer';
import FrontendTestRunner from '../Test/FrontendTestRunner.ts';
import Prompts from '../Test/Prompts.ts';
import OracleConfig from '../Common/OracleConfig.ts';

let oracleConfig = await (new OracleConfig()).readConfig();

const promptsToDisplay = Prompts.get(oracleConfig.config)
    .filter(
        promptItem => !oracleConfig.config[promptItem.name]?.length,
    );

inquirer
    .prompt(promptsToDisplay)
    .then(answers => {
        oracleConfig = oracleConfig.mergeConfig(answers)
            .setDefaults();

        const frontendTestRunner = new FrontendTestRunner(oracleConfig.config);

        frontendTestRunner.run();
    })
    .catch((error) => {
        console.warn(error);
        if (error.isTtyError) {
            // Prompt couldn't be rendered in the current environment
        } else {
            // Something else went wrong
        }
    });
