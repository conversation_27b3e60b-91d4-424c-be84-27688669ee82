import ArtemisClient from '../Lib/ArtemisClient.ts';
import fileWriter from '../Test/FileWriter.ts';
import Log from '../Common/Log.ts';
import Application from '@/backend/Lib/Application.ts';

/**
 * Fetch brands from Artemis and save them to a JSON file.
 * @param {string} filePath - The file path where the brands JSON should be saved.
 */
async function ArtemisPullBrands(filePath: string): Promise<void> {
    try {
        const brands = await ArtemisClient.get('/api/brands');

        if (!brands || brands.length === 0) {
            throw Error('No brands data received from Artemis.');
        }

        fileWriter.writeSafe(filePath, JSON.stringify(brands, null, 2), false);

        Log.info('Pulled brands from Artemis and saved to file.');
    } catch (error) {
        Log.error(`Failed to fetch or save brands: ${error.message}`);
    }
}

ArtemisPullBrands(
    Application.brandsJsonPath,
);

export default ArtemisPullBrands;
