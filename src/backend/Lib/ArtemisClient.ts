import Env from '../Common/Env.ts';
import Log from '@/backend/Common/Log.ts';

class ArtemisClient {
    static async request(endpoint: string) {
        const url = `${Env.get('ARTEMIS_PRODUCTION_URL')}${endpoint}`;
        try {
            const response = await fetch(url);

            if (!response.ok) {
                throw new Error(`Response status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            Log.error(error);
        }
    }

    static get(endpoint: string) {
        return ArtemisClient.request(endpoint);
    }
}

export default ArtemisClient;
