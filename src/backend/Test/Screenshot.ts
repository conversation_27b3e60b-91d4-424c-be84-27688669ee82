import * as fs from 'node:fs';
import {DriverInterface} from '@/backend/Test/Driver/DriverInterface.ts';
import {HTTPRequest, HTTPResponse} from 'puppeteer';
import {ScreenshotPageData} from '@/shared/types/Common/ScreenshotPageData.ts';
import PerformanceMetrics from './PerformanceMetrics.ts';
import Log from '../Common/Log.ts';

class Screenshot {
    static async take({page, data}) {
        const requests = {};
        let performanceMetrics = {};

        try {
            fs.mkdirSync(data.filePath, {recursive: true});

            // Set up page and navigation
            await Screenshot.setupPage(page, data, requests);
            const driver = data.driver as DriverInterface;
            page = await driver.preparePage(page, data);
            await page.goto(Screenshot.getUrl(data), {
                timeout: 0,
                waitUntil: 'networkidle0',
            });

            // Take screenshot and collect metrics
            await Screenshot.captureScreenshotAndMetrics(page, data, requests, performanceMetrics);
        } catch (e) {
            console.warn(e);
            Log.error(`Error taking screenshot: ${e.message}`);
        } finally {
            // Include performance metrics in the callback
            data.callback({
                requests: requests,
                performanceMetrics: performanceMetrics,
            });
        }
    };

    static getUrl(data: ScreenshotPageData) {
        return data.url.replace('user', data.user);
    }

    // Helper method to get a clean URL for logging
    static getCleanUrl(url: string): string {
        try {
            const urlObj = new URL(url);
            return `${urlObj.pathname}${urlObj.search}`;
        } catch (e) {
            return url;
        }
    }

    /**
     * Set up the page and configure request interception
     */
    private static async setupPage(page, data, requests) {
        // Set up request interception before navigation
        await page.setRequestInterception(true);

        const driver = data.driver as DriverInterface;
        page = await driver.preparePage(page, data);

        // Configure request and response handlers
        Screenshot.setupRequestHandlers(page, data, requests);

        // Navigate to the URL with error handling
        try {
            await page.goto(Screenshot.getUrl(data), {
                timeout: 30000, // 30 second timeout instead of infinite
                waitUntil: ['domcontentloaded'], // Less strict wait condition
            });

            // Wait a bit more for the page to stabilize if needed
            try {
                await page.waitForNetworkIdle({timeout: 5000, idleTime: 500});
            } catch (networkIdleError) {
                // If network idle times out, that's okay, we can still continue
                Log.warn(`Network didn't reach idle state: ${networkIdleError.message}`);
            }
        } catch (navigationError) {
            // Log the error but don't throw - we'll try to take a screenshot anyway
            Log.error(`Navigation error for ${Screenshot.getUrl(data)}: ${navigationError.message}`);
            Log.info('Attempting to continue with screenshot capture despite navigation error');
        }

        return page;
    }

    /**
     * Set up request and response handlers
     */
    private static setupRequestHandlers(page, data, requests) {
        page.on('request', (request: HTTPRequest) => {
            try {
                requests['request'] ||= [];

                // Get request details
                const resourceType = request.resourceType();
                const method = request.method();
                const headers = request.headers();
                const postData = request.postData();

                requests['request'].push({
                    url: request.url(),
                    initiator: request.initiator(),
                    resourceType,
                    method,
                    headers,
                    postData,
                    timestamp: Date.now(),
                });

                if (request.url().includes(data.testCase.brandSlug)) {
                    request.abort();
                    return;
                }

                request.continue();
            } catch (requestError) {
                // If there's an error processing the request, log it and try to continue
                Log.error(`Error handling request: ${requestError.message}`);
                try {
                    request.continue();
                } catch (continueError) {
                    Log.error(`Failed to continue request: ${continueError.message}`);
                }
            }
        });

        page.on('response', (response: HTTPResponse) => {
            try {
                requests['response'] ||= [];

                // Get response timing information
                const timing = response.timing();
                const responseTime = timing ? (timing.receiveHeadersEnd - timing.sendStart) : null;
                let contentType = '';
                let status = 0;
                let headers = {};

                // Safely get response headers and status
                try {
                    headers = response.headers() || {};
                    contentType = headers['content-type'] || '';
                    status = response.status();
                } catch (headerError) {
                    Log.warn(`Error getting response headers/status for ${response.url()}: ${headerError.message}`);
                }

                // Calculate size if available
                let size = null;
                try {
                    if (headers['content-length']) {
                        size = parseInt(headers['content-length'], 10);
                    }
                } catch (e) {
                    Log.warn(`Error parsing content length for ${response.url()}: ${e.message}`);
                }

                // Create response object with all the data we've collected
                const responseData = {
                    url: response.url(),
                    timing: {
                        start: timing?.sendStart,
                        end: timing?.receiveHeadersEnd,
                    },
                    responseTime,
                    contentType,
                    status,
                    size,
                    headers,
                    error: status >= 400 || status === 0, // Flag to indicate error responses
                };

                requests['response'].push(responseData);
            } catch (responseError) {
                // If there's an error processing the response, log it and continue
                Log.error(`Error handling response: ${responseError.message}`);
                try {
                    // Still try to record basic information about the response
                    requests['response'].push({
                        url: response.url(),
                        error: true,
                        errorMessage: responseError.message,
                    });
                } catch (e) {
                    Log.error(`Failed to record error response: ${e.message}`);
                }
            }
        });
    }

    /**
     * Capture screenshot and collect performance metrics
     */
    private static async captureScreenshotAndMetrics(page, data, requests, performanceMetrics) {
        // Clean up the page before taking screenshot
        try {
            await page.evaluate(() => {
                try {
                    if (document.body) {
                        document.body.style.overflowY = 'scroll';
                    }
                } catch (e) {
                    console.warn('Error setting overflow:', e);
                }

                // Replace ad elements
                try {
                    const csaElements = document.querySelectorAll('[id^="csa-"]');
                    csaElements.forEach((element) => {
                        if (element && element.parentNode) {
                            const originalWidth = (element as HTMLElement).offsetWidth;
                            const replacementDiv = document.createElement('div');
                            replacementDiv.textContent = 'Ads';
                            replacementDiv.style.height = `100px`;
                            replacementDiv.style.width = `${originalWidth}px`;
                            replacementDiv.style.display = 'grid';
                            replacementDiv.style.placeItems = 'center';
                            replacementDiv.style.border = '1px solid grey';
                            element.parentNode.replaceChild(replacementDiv, element);
                        }
                    });
                } catch (e) {
                    console.warn('Error replacing ad elements:', e);
                }

                // Remove focus from search bar
                try {
                    const searchBar = document.querySelector('.search-bar');
                    const input = searchBar?.querySelector('.search-bar__field-input');

                    if (input) {
                        (input as HTMLElement).blur();
                    }
                } catch (e) {
                    console.warn('Error removing focus from search bar:', e);
                }

                // Remove toolbars
                try {
                    [
                        document.querySelector('.serp-toolbar'),
                        document.querySelector('.sf-toolbar'),
                    ].forEach((element) => {
                        if (element && element.parentNode) {
                            element.parentNode.removeChild(element);
                        }
                    });
                } catch (e) {
                    console.warn('Error removing toolbars:', e);
                }

                // Replace dynamic text
                try {
                    const element = document.querySelector('#web-search-stats-title__data');
                    if (element) {
                        (element as HTMLElement).innerText = 'Text explaining results found in - x seconds';
                    }
                } catch (e) {
                    console.warn('Error replacing dynamic text:', e);
                }
            });
        } catch (evaluateError) {
            // If page evaluation fails, log the error but continue
            Log.warn(`Error preparing page for screenshot: ${evaluateError.message}`);
        }

        // Take the screenshot with error handling
        try {
            await page.screenshot({
                path: `${data.filePath}/${data.test}.png`,
                fullPage: true, // Capture the full page even if some elements failed to load
                timeout: 5000, // Add a timeout to prevent hanging
            });
        } catch (screenshotError) {
            // If screenshot fails, log the error but continue
            Log.error(`Failed to take screenshot: ${screenshotError.message}`);

            // Try to take a screenshot of just the viewport as a fallback
            try {
                await page.screenshot({
                    path: `${data.filePath}/${data.test}.png`,
                    fullPage: false,
                    timeout: 5000,
                });
            } catch (fallbackError) {
                Log.error(`Failed to take fallback screenshot: ${fallbackError.message}`);

                // Create an error image to indicate the failure
                try {
                    const fs = require('fs');
                    const errorMessage = `Failed to capture screenshot: ${screenshotError.message}`;
                    fs.writeFileSync(`${data.filePath}/${data.test}.txt`, errorMessage);
                } catch (fileError) {
                    Log.error(`Failed to create error file: ${fileError.message}`);
                }
            }
        }

        // Collect performance metrics with error handling
        try {
            const metrics = await PerformanceMetrics.collectMetrics(page);
            Object.assign(performanceMetrics, metrics);
            Log.info(`Successfully collected performance metrics for ${data.testCase.brandSlug}`);
        } catch (metricsError) {
            // If metrics collection fails, log the error but continue
            Log.error(`Failed to collect performance metrics: ${metricsError.message}`);
            // Set some default values to prevent null reference errors
            performanceMetrics.load = 0;
            performanceMetrics.domContentLoaded = 0;
            performanceMetrics.totalRequests = requests.request ? requests.request.length : 0;
        }

        return performanceMetrics;
    }
}

export default Screenshot;
