import TestCase from './TestCase.ts';
import * as path from 'node:path';
import {find} from 'lodash-es';
import routes from '@/shared/config/routes.ts';
import type Route from '@/shared/types/Config/Route.ts';
import UrlSet from '@/shared/types/Test/UrlSet.ts';
import Browser from '@/shared/types/Config/Browser.ts';
import Viewport from '@/shared/types/Config/Viewport.ts';
import {DriverInterface} from '../Test/Driver/DriverInterface.ts';
import Application from '@/backend/Lib/Application.ts';

class TestCaseFactory {
    static readonly urlRegex = /^(?:https?:\/\/)?([^/]+)/;

    static create(
        date: string,
        brandSlug: string,
        urlSet: UrlSet,
        browser: Browser,
        viewport: Viewport,
        driver: DriverInterface,
        user: string,
    ) {
        const screenshotPath = Application.screenshotPath;

        const urlName = find(routes, (route: Route) => route.value === urlSet.url)
            .name
            .replace('/', '')
            .trim();
        const fullUrl = RegExp(this.urlRegex).exec(urlSet.production).at(1);
        const filePath = path.normalize(
            `${screenshotPath}/${date}/${brandSlug}/${driver.name}/${browser.product}/${urlName}/${viewport.name}/${fullUrl}`,
        );

        return new TestCase(
            date,
            filePath,
            urlSet,
            brandSlug,
            driver,
            browser,
            viewport,
            user,
        );
    }
}

export default TestCaseFactory;
