import TestCase from './TestCase.ts';
import * as path from 'node:path';
import {find} from 'lodash-es';
import routes from '@/shared/config/routes.ts';
import type Route from '@/shared/types/Config/Route.ts';
import UrlSet from '@/shared/types/Test/UrlSet.ts';
import Browser from '@/shared/types/Config/Browser.ts';
import Viewport from '@/shared/types/Config/Viewport.ts';
import {DriverInterface} from '../Test/Driver/DriverInterface.ts';
import Application from '@/backend/Lib/Application.ts';

class TestCaseFactory {
    private static readonly urlRegex = /^(?:https?:\/\/)?([^/]+)/;
    private static readonly defaultUrlName = 'unknown';

    static create(
        date: string,
        brandSlug: string,
        urlSet: UrlSet,
        browser: Browser,
        viewport: Viewport,
        driver: DriverInterface,
        user: string,
    ): TestCase {
        this.validateInputs(date, brandSlug, urlSet, browser, viewport, driver, user);

        try {
            const screenshotPath = Application.screenshotPath;
            const urlName = this.extractUrlName(urlSet.url);
            const fullUrl = this.extractDomainFromUrl(urlSet.production);

            const filePath = path.normalize(
                `${screenshotPath}/${date}/${brandSlug}/${driver.name}/${browser.product}/${urlName}/${viewport.name}/${fullUrl}`,
            );

            return new TestCase(
                date,
                filePath,
                urlSet,
                brandSlug,
                driver,
                browser,
                viewport,
                user,
            );
        } catch (error) {
            throw new Error(`Failed to create test case: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    private static validateInputs(
        date: string,
        brandSlug: string,
        urlSet: UrlSet,
        browser: Browser,
        viewport: Viewport,
        driver: DriverInterface,
        user: string,
    ): void {
        if (!date || typeof date !== 'string') {
            throw new Error('Valid date string is required');
        }
        if (!brandSlug || typeof brandSlug !== 'string') {
            throw new Error('Valid brandSlug string is required');
        }
        if (!urlSet || typeof urlSet !== 'object') {
            throw new Error('Valid UrlSet object is required');
        }
        if (!browser || typeof browser !== 'object') {
            throw new Error('Valid Browser object is required');
        }
        if (!viewport || typeof viewport !== 'object') {
            throw new Error('Valid Viewport object is required');
        }
        if (!driver || typeof driver !== 'object') {
            throw new Error('Valid DriverInterface object is required');
        }
        if (!user || typeof user !== 'string') {
            throw new Error('Valid user string is required');
        }
    }

    private static extractUrlName(url: string): string {
        try {
            const route = find(routes, (route: Route) => route.value === url);

            if (!route || !route.name) {
                console.warn(`Route not found for URL: ${url}, using default name`);
                return this.defaultUrlName;
            }

            return route.name.replace('/', '').trim() || this.defaultUrlName;
        } catch (error) {
            console.warn(`Error extracting URL name for ${url}:`, error);
            return this.defaultUrlName;
        }
    }

    private static extractDomainFromUrl(productionUrl: string): string {
        try {
            const match = this.urlRegex.exec(productionUrl);
            const domain = match?.[1];

            if (!domain) {
                throw new Error(`Could not extract domain from URL: ${productionUrl}`);
            }

            return domain;
        } catch (error) {
            throw new Error(`Failed to extract domain from URL ${productionUrl}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
}

export default TestCaseFactory;
