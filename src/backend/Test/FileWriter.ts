import * as fs from 'node:fs';

class FileWriter {
    static writeSafe(fileName: string, contents: string = '', appendToFile: boolean = true): void {
        const parts = fileName.split('/');
        parts.pop();
        fs.mkdirSync(parts.join('/'), {recursive: true});
        if (!fs.existsSync(fileName)) {
            fs.writeFileSync(fileName, '');
        }
        appendToFile === true
            ? fs.appendFileSync(fileName, contents)
            : fs.writeFileSync(fileName, contents);
    }
}

export default FileWriter;
