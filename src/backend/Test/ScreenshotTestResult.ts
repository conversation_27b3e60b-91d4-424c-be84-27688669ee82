import TestCase from './TestCase.ts';
import fileWriter from './FileWriter.ts';

class ScreenshotTestResult {
    public testCase: TestCase;
    public diffAmount: number;
    public passing: boolean;

    constructor(testCase: TestCase, diffAmount: number, passing: boolean) {
        this.testCase = testCase;
        this.diffAmount = diffAmount;
        this.passing = passing;

        this.createMetaFile();
    }

    createMetaFile() {
        fileWriter.writeSafe(`${this.testCase.filePath}/meta.json`, JSON.stringify(this.toAuditData()));
    }

    toAuditData() {
        return {
            testCase: this.testCase.toAuditData(),
            diffAmount: this.diffAmount,
            passing: this.passing,
        };
    }
}

export default ScreenshotTestResult;
