import UrlSet from '@/shared/types/Test/UrlSet.ts';
import Browser from '@/shared/types/Config/Browser.ts';
import Viewport from '@/shared/types/Config/Viewport.ts';
import {Cluster} from 'puppeteer-cluster';
import {DriverInterface} from '../Test/Driver/DriverInterface.ts';

class TestCase {
    cluster: Cluster;
    date: string;
    filePath: string;
    urlSet: UrlSet;
    brandSlug: string;
    driver: DriverInterface;
    browser: Browser;
    viewport: Viewport;
    user: string;
    private screenshotResponse: any;
    private loadMetrics: any;

    constructor(
        date: string,
        filePath: string,
        urlSet: UrlSet,
        brandSlug: string,
        driver: DriverInterface,
        browser: Browser,
        viewport: Viewport,
        user: string,
    ) {
        this.date = date;
        this.filePath = filePath;
        this.urlSet = urlSet;
        this.brandSlug = brandSlug;
        this.browser = browser;
        this.viewport = viewport;
        this.driver = driver;
        this.user = user;
    }

    runOnCluster(cluster: Cluster) {
        this.cluster = cluster;

        return this;
    }

    onScreenshotResult(environment: string, response: any) {
        this.screenshotResponse ||= {};
        this.screenshotResponse[environment] = response;

        // Store performance metrics separately
        if (response.performanceMetrics) {
            this.loadMetrics ||= {};
            this.loadMetrics[environment] = response.performanceMetrics;
        }
    }

    toAuditData() {
        return {
            date: this.date,
            filePath: this.filePath,
            urlSet: this.urlSet,
            brandSlug: this.brandSlug,
            driver: this.driver,
            browser: this.browser,
            viewport: this.viewport,
            user: this.user,
            screenshotResponse: this.screenshotResponse,
            loadMetrics: this.loadMetrics,
        };
    }
}

export default TestCase;
