import UrlSet from '@/shared/types/Test/UrlSet.ts';
import Browser from '@/shared/types/Config/Browser.ts';
import Viewport from '@/shared/types/Config/Viewport.ts';
import {Cluster} from 'puppeteer-cluster';
import {DriverInterface} from '../Test/Driver/DriverInterface.ts';

interface ScreenshotResponse {
    [environment: string]: any;
}

interface LoadMetrics {
    [environment: string]: any;
}

interface AuditData {
    date: string;
    filePath: string;
    urlSet: UrlSet;
    brandSlug: string;
    driver: DriverInterface;
    browser: Browser;
    viewport: Viewport;
    user: string;
    screenshotResponse?: ScreenshotResponse;
    loadMetrics?: LoadMetrics;
}

class TestCase {
    public readonly cluster: Cluster;
    public readonly date: string;
    public readonly filePath: string;
    public readonly urlSet: UrlSet;
    public readonly brandSlug: string;
    public readonly driver: DriverInterface;
    public readonly browser: Browser;
    public readonly viewport: Viewport;
    public readonly user: string;
    private screenshotResponse?: ScreenshotResponse;
    private loadMetrics?: LoadMetrics;

    constructor(
        date: string,
        filePath: string,
        urlSet: UrlSet,
        brandSlug: string,
        driver: DriverInterface,
        browser: Browser,
        viewport: Viewport,
        user: string,
    ) {
        this.validateInputs(date, filePath, urlSet, brandSlug, driver, browser, viewport, user);

        this.date = date;
        this.filePath = filePath;
        this.urlSet = urlSet;
        this.brandSlug = brandSlug;
        this.browser = browser;
        this.viewport = viewport;
        this.driver = driver;
        this.user = user;
    }

    private validateInputs(
        date: string,
        filePath: string,
        urlSet: UrlSet,
        brandSlug: string,
        driver: DriverInterface,
        browser: Browser,
        viewport: Viewport,
        user: string,
    ): void {
        if (!date || typeof date !== 'string') {
            throw new Error('Valid date string is required');
        }
        if (!filePath || typeof filePath !== 'string') {
            throw new Error('Valid filePath string is required');
        }
        if (!urlSet || typeof urlSet !== 'object') {
            throw new Error('Valid UrlSet object is required');
        }
        if (!brandSlug || typeof brandSlug !== 'string') {
            throw new Error('Valid brandSlug string is required');
        }
        if (!driver || typeof driver !== 'object') {
            throw new Error('Valid DriverInterface object is required');
        }
        if (!browser || typeof browser !== 'object') {
            throw new Error('Valid Browser object is required');
        }
        if (!viewport || typeof viewport !== 'object') {
            throw new Error('Valid Viewport object is required');
        }
        if (!user || typeof user !== 'string') {
            throw new Error('Valid user string is required');
        }
    }

    public runOnCluster(cluster: Cluster): TestCase {
        if (!cluster) {
            throw new Error('Valid cluster is required');
        }

        // Note: We need to cast this to any because cluster is readonly but we need to set it
        (this as any).cluster = cluster;
        return this;
    }

    public onScreenshotResult(environment: string, response: any): void {
        if (!environment || typeof environment !== 'string') {
            throw new Error('Valid environment string is required');
        }
        if (!response) {
            throw new Error('Valid response is required');
        }

        try {
            this.screenshotResponse = this.screenshotResponse || {};
            this.screenshotResponse[environment] = response;

            // Store performance metrics separately
            if (response.performanceMetrics) {
                this.loadMetrics = this.loadMetrics || {};
                this.loadMetrics[environment] = response.performanceMetrics;
            }
        } catch (error) {
            throw new Error(`Failed to process screenshot result for environment ${environment}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    public toAuditData(): AuditData {
        return {
            date: this.date,
            filePath: this.filePath,
            urlSet: this.urlSet,
            brandSlug: this.brandSlug,
            driver: this.driver,
            browser: this.browser,
            viewport: this.viewport,
            user: this.user,
            screenshotResponse: this.screenshotResponse,
            loadMetrics: this.loadMetrics,
        };
    }

    public getScreenshotResponse(): ScreenshotResponse | undefined {
        return this.screenshotResponse ? { ...this.screenshotResponse } : undefined;
    }

    public getLoadMetrics(): LoadMetrics | undefined {
        return this.loadMetrics ? { ...this.loadMetrics } : undefined;
    }

    public hasScreenshotForEnvironment(environment: string): boolean {
        return !!(this.screenshotResponse && this.screenshotResponse[environment]);
    }

    public hasLoadMetricsForEnvironment(environment: string): boolean {
        return !!(this.loadMetrics && this.loadMetrics[environment]);
    }
}

export default TestCase;
