import os from 'os';
import {map} from 'lodash-es';
import viewports from '@/shared/config/viewports.ts';
import BrandWebsitesBrandsReader from './BrandWebsitesBrandsReader.ts';
import browsers from '@/shared/config/browsers.ts';
import routes from '@/shared/config/routes.ts';
import OracleConfig from '@/shared/types/Common/OracleConfig.ts';
import drivers from '@/shared/config/drivers.ts';

let prompts = [
    {
        name: 'user',
        message: 'What is your user? (user.ldev.nl)',
        default: os.userInfo().username?.replaceAll('.', ''),
    },
    {
        type: 'checkbox',
        name: 'urls',
        message: 'Select the URLs that need to be tested:',
        choices: routes,
    },
    {
        type: 'checkbox',
        name: 'viewports',
        message: 'Select the viewports that need to be tested:',
        choices: map(viewports, (viewport) => viewport.name),
    },
    {
        type: 'checkbox',
        name: 'browsers',
        message: 'Select the browsers that need to be tested:',
        choices: map(browsers, (browser) => browser.product),
    },
    {
        type: 'checkbox',
        name: 'drivers',
        message: 'Select the drivers:',
        choices: drivers,
    },
    {
        type: 'checkbox',
        name: 'brands',
        message: 'Select the brands that need to be tested:',
        choices: map(BrandWebsitesBrandsReader.getAllBrands(), brand => ({
            name: brand.slug,
            value: brand.slug,
        }))
            .sort(),
    },
];

class Prompts {
    static get(oracleConfig: OracleConfig) {
        const usedArguments = {};
        process.argv.slice(2)
            .forEach(param => {
                const paramParts = param.replace('--', '').split('=');

                if (paramParts.length === 2) {
                    usedArguments[paramParts.at(0)] = paramParts.at(-1);
                } else if (paramParts.length === 1) {
                    // Flag
                    usedArguments[paramParts.at(-1)] = true;
                }
            });

        if (usedArguments['default-user'] || oracleConfig.user) {
            prompts = prompts.filter(prompt => prompt.name !== 'user');
        }

        return prompts;
    }
}

export default Prompts;
