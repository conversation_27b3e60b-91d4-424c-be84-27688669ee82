// Define a custom PerformanceEntry interface since it's not exported from puppeteer
interface PerformanceEntry {
    name: string;
    entryType: string;
    startTime: number;
    duration: number;
    transferSize?: number;
    encodedBodySize?: number;
    initiatorType?: string;
}

export interface PagePerformanceMetrics {
    domContentLoaded?: number;
    load?: number;
    firstPaint?: number;
    firstContentfulPaint?: number;
    totalRequests?: number;
    totalBytes?: number;
    timeToFirstByte?: number;
    timeToInteractive?: number;
    largestContentfulPaint?: number;
    cumulativeLayoutShift?: number;
    totalBlockingTime?: number;
    speedIndex?: number;
    requestTimings?: {
        [url: string]: {
            startTime: number;
            endTime: number;
            duration: number;
            size?: number;
            type?: string;
        };
    };
}

class PerformanceMetrics {
    /**
     * Collect performance metrics from the page
     */
    static async collectMetrics(page): Promise<PagePerformanceMetrics> {
        try {
            // Get performance metrics from the page
            const metrics = await page.metrics();

            // Get performance timing from the page
            const performanceTiming = await page.evaluate(() => {
                return {
                    navigationStart: performance.timing.navigationStart,
                    domContentLoaded: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart,
                    load: performance.timing.loadEventEnd - performance.timing.navigationStart,
                    ttfb: performance.timing.responseStart - performance.timing.navigationStart,
                    domInteractive: performance.timing.domInteractive - performance.timing.navigationStart,
                };
            });

            // Get performance entries from the page
            const performanceEntries = await page.evaluate(() => {
                return JSON.parse(JSON.stringify(performance.getEntries()));
            });

            // Process request timings
            const requestTimings = {};
            performanceEntries.forEach((entry: PerformanceEntry) => {
                if (entry.entryType === 'resource') {
                    requestTimings[entry.name] = {
                        startTime: entry.startTime,
                        endTime: entry.startTime + entry.duration,
                        duration: entry.duration,
                        size: entry.transferSize || entry.encodedBodySize,
                        type: entry.initiatorType,
                    };
                }
            });

            // Get paint metrics
            const paintMetrics = performanceEntries
                .filter((entry: PerformanceEntry) => entry.entryType === 'paint')
                .reduce((acc, entry: PerformanceEntry) => {
                    acc[entry.name] = entry.startTime;
                    return acc;
                }, {});

            // Get largest contentful paint
            const lcpEntry = performanceEntries
                .filter((entry: PerformanceEntry) => entry.entryType === 'largest-contentful-paint')
                .pop();

            // Get cumulative layout shift
            const clsEntries = performanceEntries
                .filter((entry: PerformanceEntry) => entry.entryType === 'layout-shift');
            const cumulativeLayoutShift = clsEntries.reduce((acc, entry: any) => acc + entry.value, 0);

            return {
                domContentLoaded: performanceTiming.domContentLoaded,
                load: performanceTiming.load,
                firstPaint: paintMetrics['first-paint'],
                firstContentfulPaint: paintMetrics['first-contentful-paint'],
                timeToFirstByte: performanceTiming.ttfb,
                timeToInteractive: performanceTiming.domInteractive,
                largestContentfulPaint: lcpEntry ? lcpEntry.startTime : undefined,
                cumulativeLayoutShift,
                totalRequests: Object.keys(requestTimings).length,
                totalBytes: Object.values(requestTimings).reduce((acc: number, timing: any) => acc + (timing.size || 0), 0) as number,
                requestTimings,
            };
        } catch (error) {
            console.error('Error collecting performance metrics:', error);
            return {};
        }
    }
}

export default PerformanceMetrics;
