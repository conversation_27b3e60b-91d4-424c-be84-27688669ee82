import dayjs from 'dayjs';
import TestCleaner from './TestCleaner.ts';
import OracleConfig from '@/shared/types/Common/OracleConfig.ts';
import DatestampsHandler from './DatestampsHandler.ts';
import ProgressBar from './ProgressBar.ts';
import AuditReporter from './AuditReporter.ts';
import TestCaseCreator from './TestCaseCreator.ts';
import ClusterManager from './ClusterManager.ts';
import Log from '@/backend/Common/Log.ts';

interface TestRunnerConfig {
    environments?: string[];
    maxConcurrency?: number;
    cleanupAfterRun?: boolean;
}

interface TestRunResult {
    date: string;
    totalTestCases: number;
    executionTime: number;
    success: boolean;
    error?: string;
}

class FrontendTestRunner {
    private readonly environments: readonly string[];
    private readonly testCaseCreator: TestCaseCreator;
    private readonly clusterManager: ClusterManager;
    private readonly config: Required<TestRunnerConfig>;

    constructor(oracleConfig: OracleConfig, config: TestRunnerConfig = {}) {
        if (!oracleConfig) {
            throw new Error('OracleConfig is required');
        }

        this.config = {
            environments: config.environments || ['production', 'local'],
            maxConcurrency: config.maxConcurrency || 25,
            cleanupAfterRun: config.cleanupAfterRun ?? true,
        };

        this.environments = [...this.config.environments];
        this.clusterManager = new ClusterManager(this.config.environments, this.config.maxConcurrency);
        this.testCaseCreator = new TestCaseCreator(oracleConfig, this.clusterManager);
    }

    public async run(): Promise<TestRunResult> {
        const startTime = Date.now();
        const date = dayjs().format('YYYY-MM-DD--HH:mm');

        Log.info(`Starting test run for date: ${date}`);

        try {
            const result = await this.executeTestRun(date);
            const executionTime = Date.now() - startTime;

            Log.info(`Test run completed successfully in ${executionTime}ms`);

            return {
                ...result,
                executionTime,
                success: true,
            };
        } catch (error) {
            const executionTime = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';

            Log.error(`Test run failed after ${executionTime}ms: ${errorMessage}`);

            return {
                date,
                totalTestCases: 0,
                executionTime,
                success: false,
                error: errorMessage,
            };
        } finally {
            if (this.config.cleanupAfterRun) {
                await this.cleanup();
            }
        }
    }

    private async executeTestRun(date: string): Promise<Omit<TestRunResult, 'executionTime' | 'success'>> {
        // Create test cases
        await this.testCaseCreator.createTestCases(date);
        const testCases = this.testCaseCreator.getTestCases();

        if (testCases.length === 0) {
            throw new Error('No test cases were created');
        }

        Log.info(`Created ${testCases.length} test cases`);

        // Initialize progress tracking
        const screenshotProgress = ProgressBar.create();
        const totalOperations = testCases.length * this.environments.length;
        screenshotProgress.start(totalOperations, 0);

        try {
            // Run test cases
            await this.clusterManager.runTestCases([...testCases], screenshotProgress);
            screenshotProgress.stop();

            // Generate reports
            await this.finalizeAuditReport(date);
            await this.handleDatestamps(date);

            return {
                date,
                totalTestCases: testCases.length,
            };
        } catch (error) {
            screenshotProgress.stop();
            throw error;
        }
    }

    private async cleanup(): Promise<void> {
        try {
            await this.clusterManager.cleanupClusters();
            Log.info('Cleanup completed successfully');
        } catch (error) {
            Log.error(`Cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    private async finalizeAuditReport(date: string): Promise<void> {
        try {
            await AuditReporter.report(this.testCaseCreator.getTestCases(), date);
        } catch (error) {
            throw new Error(`Failed to generate audit report: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    private async handleDatestamps(date: string): Promise<void> {
        try {
            const datestampsHandler = new DatestampsHandler();
            await datestampsHandler.updateDatestamps(date);
            const previouslyRanTestsArray = await datestampsHandler.getDatestamps();
            TestCleaner.cleanup(previouslyRanTestsArray);
        } catch (error) {
            throw new Error(`Failed to handle datestamps: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    public getEnvironments(): readonly string[] {
        return [...this.environments];
    }

    public getConfig(): Readonly<Required<TestRunnerConfig>> {
        return { ...this.config };
    }

    public getTestCases() {
        return this.testCaseCreator.getTestCases();
    }
}

export default FrontendTestRunner;
