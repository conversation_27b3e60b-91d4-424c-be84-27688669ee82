import dayjs from 'dayjs';
import TestCleaner from './TestCleaner.ts';
import OracleConfig from '@/shared/types/Common/OracleConfig.ts';
import DatestampsHandler from './DatestampsHandler.ts';
import ProgressBar from './ProgressBar.ts';
import AuditReporter from './AuditReporter.ts';
import TestCaseCreator from './TestCaseCreator.ts';
import ClusterManager from './ClusterManager.ts';

class FrontendTestRunner {
    private readonly environments: string[] = ['production', 'local'];

    private readonly testCaseCreator: TestCaseCreator;
    private readonly clusterManager: ClusterManager;

    constructor(oracleConfig: OracleConfig) {
        this.clusterManager = new ClusterManager(
            this.environments,
        );
        this.testCaseCreator = new TestCaseCreator(
            oracleConfig,
            this.clusterManager,
        );
    }

    async run() {
        const date = dayjs().format('YYYY-MM-DD--HH:mm');
        const screenshotProgress = ProgressBar.create();
        await this.testCaseCreator.createTestCases(date);

        const testCases = this.testCaseCreator.getTestCases();
        screenshotProgress.start(testCases.length * this.environments.length, 0);

        await this.clusterManager.runTestCases(testCases, screenshotProgress);
        await this.clusterManager.cleanupClusters();

        screenshotProgress.stop();

        await this.finalizeAuditReport(date);
        await this.handleDatestamps(date);
    }

    private async finalizeAuditReport(date: string) {
        await AuditReporter.report(this.testCaseCreator.getTestCases(), date);
    }

    private async handleDatestamps(date: string) {
        const datestampsHandler = new DatestampsHandler();
        await datestampsHandler.updateDatestamps(date);
        const previouslyRanTestsArray = await datestampsHandler.getDatestamps();
        TestCleaner.cleanup(previouslyRanTestsArray);
    }
}

export default FrontendTestRunner;
