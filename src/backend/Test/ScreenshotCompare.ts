import {PNG} from 'pngjs';
import * as fs from 'node:fs';
import FileWriter from './FileWriter.ts';
import pixelmatch from 'pixelmatch';
import ScreenshotTestResult from './ScreenshotTestResult.ts';
import TestCase from './TestCase.ts';
import * as path from 'node:path';

interface ComparisonConfig {
    diffThreshold?: number;
    maxDiffInPixels?: number;
    outputDiffImage?: boolean;
}

interface ComparisonResult {
    diffAmount: number;
    passing: boolean;
    diffImagePath?: string;
}

class ScreenshotCompare {
    private static readonly defaultConfig: Required<ComparisonConfig> = {
        diffThreshold: 0.15,
        maxDiffInPixels: 10,
        outputDiffImage: true,
    };

    public static async testCase(
        testCase: TestCase,
        config: ComparisonConfig = {}
    ): Promise<ScreenshotTestResult> {
        if (!testCase) {
            throw new Error('TestCase is required');
        }

        const mergedConfig = { ...this.defaultConfig, ...config };

        try {
            const result = await this.compareScreenshots(testCase.filePath, mergedConfig);
            return new ScreenshotTestResult(testCase, result.diffAmount, result.passing);
        } catch (error) {
            throw new Error(`Failed to test case: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    private static async compareScreenshots(
        filePath: string,
        config: Required<ComparisonConfig>
    ): Promise<ComparisonResult> {
        if (!filePath || typeof filePath !== 'string') {
            throw new Error('Valid file path is required');
        }

        try {
            const diffAmount = await this.calculateDiff(filePath, config);
            const passing = diffAmount <= config.maxDiffInPixels;

            return {
                diffAmount,
                passing,
                diffImagePath: config.outputDiffImage ? this.getDiffImagePath(filePath) : undefined,
            };
        } catch (error) {
            throw new Error(`Failed to compare screenshots: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    private static async calculateDiff(
        filePath: string,
        config: Required<ComparisonConfig>
    ): Promise<number> {
        try {
            // Ensure directory exists
            fs.mkdirSync(filePath, { recursive: true });

            const localImagePath = path.join(filePath, 'local.png');
            const productionImagePath = path.join(filePath, 'production.png');
            const diffImagePath = path.join(filePath, 'diff.png');

            // Check if both images exist
            if (!fs.existsSync(localImagePath) || !fs.existsSync(productionImagePath)) {
                console.warn(`Missing images for comparison at ${filePath}`);
                return 0;
            }

            // Read and parse images
            const localImage = PNG.sync.read(fs.readFileSync(localImagePath));
            const productionImage = PNG.sync.read(fs.readFileSync(productionImagePath));

            // Validate image dimensions
            if (localImage.width !== productionImage.width || localImage.height !== productionImage.height) {
                throw new Error(`Image dimensions mismatch: local(${localImage.width}x${localImage.height}) vs production(${productionImage.width}x${productionImage.height})`);
            }

            const { width, height } = localImage;
            const diff = new PNG({ width, height });

            // Calculate pixel differences
            const diffAmount = pixelmatch(
                localImage.data,
                productionImage.data,
                diff.data,
                width,
                height,
                {
                    threshold: config.diffThreshold,
                }
            );

            // Save diff image if configured
            if (config.outputDiffImage) {
                FileWriter.writeSafe(diffImagePath, PNG.sync.write(diff) as unknown as string, false);
            }

            return diffAmount;
        } catch (error) {
            throw new Error(`Failed to calculate diff for ${filePath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    private static getDiffImagePath(filePath: string): string {
        return path.join(filePath, 'diff.png');
    }

    public static getDefaultConfig(): Required<ComparisonConfig> {
        return { ...this.defaultConfig };
    }
}

export default ScreenshotCompare;
