import {PNG} from 'pngjs';
import * as fs from 'node:fs';
import FileWriter from './FileWriter.ts';
import pixelmatch from 'pixelmatch';
import ScreenshotTestResult from './ScreenshotTestResult.ts';
import TestCase from './TestCase.ts';

class ScreenshotCompare {
    private static readonly diffThreshold = 0.15;

    private static readonly maxDiffInPixels = 10;

    static async testCase(testCase: TestCase) {
        const filePath = testCase.filePath;

        const diffAmount = ScreenshotCompare.calculateDiff(filePath);
        const passing = diffAmount <= ScreenshotCompare.maxDiffInPixels;

        return new ScreenshotTestResult(testCase, diffAmount, passing);
    }

    static calculateDiff(filePath: string) {
        fs.mkdirSync(filePath, {recursive: true});

        const img1 = PNG.sync.read(
            fs.readFileSync(`${filePath}/local.png`),
        );

        const img2 = PNG.sync.read(
            fs.readFileSync(`${filePath}/production.png`),
        );

        const {width, height} = img1;
        const diff = new PNG({width, height});

        const diffInPixels = pixelmatch(
            img1.data,
            img2.data,
            diff.data,
            width,
            height,
            {
                threshold: ScreenshotCompare.diffThreshold,
            },
        );

        FileWriter.writeSafe(
            `${filePath}/diff.png`,
            PNG.sync.write(diff) as unknown as string,
            false,
        );

        return diffInPixels;
    }
}

export default ScreenshotCompare;
