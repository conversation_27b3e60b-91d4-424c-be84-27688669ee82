import {DriverFactory} from './Driver/DriverFactory.ts';
import UrlSetFactory from './UrlSetFactory.ts';
import FileWriter from './FileWriter.ts';
import TestCase from './TestCase.ts';
import {DriverInterface} from './Driver/DriverInterface.ts';
import OracleConfig from '@/shared/types/Common/OracleConfig.ts';
import brands from '@/shared/config/overrides/brands.ts';
import TestCaseFactory from './TestCaseFactory.ts';
import ClusterManager from './ClusterManager.ts';
import Brand from '@/backend/Common/Brand.ts';
import {uniq} from 'lodash-es';
import Application from '@/backend/Lib/Application.ts';

class TestCaseCreator {
    private testCases: TestCase[] = [];
    private oracleConfig: OracleConfig;
    private clusterManager: ClusterManager;

    constructor(config: OracleConfig, clusterManager: ClusterManager) {
        this.oracleConfig = config;
        this.clusterManager = clusterManager;
    }

    public createTestItems(date: string) {
        const screenshotPath = Application.screenshotPath;
        const testItems = this.oracleConfig.brands.reduce((config, brandConfig) => {
            const domains = this.getDomains(brandConfig);
            config[brandConfig.slug] = domains.map(domain => ({
                urls: this.oracleConfig.urls.map(url => UrlSetFactory.create(domain, url)),
                browsers: this.oracleConfig.browsers,
                viewports: this.oracleConfig.viewports,
                drivers: this.oracleConfig.drivers,
                brand: brandConfig,
            }));

            return config;
        }, {});

        FileWriter.writeSafe(
            `${screenshotPath}/${date}/test.json`,
            JSON.stringify(testItems, undefined, 2),
            false,
        );

        return testItems;
    }

    public getTestCases() {
        return this.testCases;
    }

    async createTestCases(date: string) {
        const drivers = this.createDrivers();

        const testItems = this.createTestItems(date);

        for (const [brandSlug, brandConfigs] of Object.entries(testItems) as [string, any][]) {
            for (const brandConfig of brandConfigs) {
                const combinations = this.combineArrays([drivers, this.oracleConfig.browsers, this.oracleConfig.viewports, brandConfig.urls]);

                for (const combination of combinations) {
                    const [driver, browser, viewport, url] = combination;
                    const cluster = await this.clusterManager.initClusterForDriver(driver, browser);

                    const testCase = TestCaseFactory.create(
                        date, brandSlug, url, browser, viewport, driver, this.oracleConfig.user,
                    ).runOnCluster(cluster);

                    this.testCases.push(testCase);
                }
            }
        }
    }

    private getDomains(brandConfig: Brand): string[] {
        const brandConfigOverrides = brands[brandConfig.slug] || null;
        let additionalDomains: string[] = [];
        if (brandConfigOverrides) {
            additionalDomains = Array.isArray(brandConfigOverrides.additionalDomains)
                ? brandConfigOverrides.additionalDomains
                    .map((subDomain: string) => brandConfig.getSubDomainByPrefix(subDomain))
                    .filter((domain: string | null) => domain !== null) as string[]
                : [];
        }

        return uniq([
            brandConfig.getSubDomainByPrefix(brandConfig.getSubDomain()),
            ...additionalDomains,
        ]) as string[];
    }

    private createDrivers(): DriverInterface[] {
        return this.oracleConfig.drivers.map(driverName => DriverFactory.createDriver(driverName));
    }

    private combineArrays(arrays: any[]): any[] {
        if (arrays.length === 0) return [[]];
        const [firstArray, ...rest] = arrays;
        const restCombinations = this.combineArrays(rest);
        return firstArray.flatMap(item => restCombinations.map(combination => [item, ...combination]));
    }
}

export default TestCaseCreator;
