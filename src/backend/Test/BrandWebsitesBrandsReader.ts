import * as fs from 'node:fs';
import {filter, map, sampleSize} from 'lodash-es';
import Brand from '../Common/Brand.ts';
import Application from '@/backend/Lib/Application.ts';

class BrandWebsitesBrandsReader {
    static getAllBrands() {
        if (!fs.existsSync(Application.brandsJsonPath)) {
            throw Error('Brands have not been pulled from Artemis. Please run `yarn app:artemis:brands` first.');
        }

        const brandsJson = fs.readFileSync(Application.brandsJsonPath, 'utf8');

        return map(JSON.parse(brandsJson).brands, brand => new Brand(brand));
    }

    static getRandomBrands(brandCount: number) {
        if (brandCount <= 0) {
            throw Error('Invalid brand count passed, unable to get random brands from Brand Websites');
        }

        return sampleSize(BrandWebsitesBrandsReader.getAllBrands(), brandCount);
    }

    static getBySlug(brandSlug: string) {
        return filter(BrandWebsitesBrandsReader.getAllBrands(), brand => brand.slug === brandSlug).at(0);
    }

    static getBrands(brands: string[]) {
        return map(
            BrandWebsitesBrandsReader.getAllBrands()
                .filter(brand => brands.includes(brand.slug)),
            brand => new Brand(brand),
        );
    }
}

export default BrandWebsitesBrandsReader;
