import * as fs from 'node:fs';
import FileWriter from './FileWriter.ts';
import Application from '@/backend/Lib/Application.ts';

class DatestampsHandler {
    private datestampsPath: string;

    constructor() {
        this.datestampsPath = `${Application.screenshotPath}/_datestamps.json`;

        this.init();
    }

    async getDatestamps() {
        let datestampsArray: string[] = [];

        try {
            if (fs.existsSync(this.datestampsPath)) {
                const datestamps = await fs.promises.readFile(this.datestampsPath);
                datestampsArray = JSON.parse(datestamps.toString());
            } else {
                FileWriter.writeSafe(this.datestampsPath, JSON.stringify([]), false);
            }
        } catch (error) {
            console.error('Error reading datestamps:', error);
        }

        return datestampsArray;
    }

    async updateDatestamps(newDatestamp: string) {
        const datestampsArray = await this.getDatestamps();
        datestampsArray.push(newDatestamp);
        await FileWriter.writeSafe(this.datestampsPath, JSON.stringify(datestampsArray), false);
    }

    private init() {
        if (!fs.existsSync(this.datestampsPath)) {
            FileWriter.writeSafe(this.datestampsPath, JSON.stringify([]), false);
        }
    }
}

export default DatestampsHandler;
