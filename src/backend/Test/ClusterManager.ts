import Screenshot from './Screenshot.ts';
import {ScreenshotPageData} from '@/shared/types/Common/ScreenshotPageData.ts';
import {DriverInterface} from './Driver/DriverInterface.ts';
import type Browser from '@/shared/types/Config/Browser.ts';
import type TestCase from './TestCase.ts';

interface ClusterInfo {
    cluster: any;
    key: string;
    driver: DriverInterface;
    browser: Browser;
}

interface ProgressTracker {
    increment(): void;
}

class ClusterManager {
    private readonly clusters: ClusterInfo[] = [];
    private readonly environments: readonly string[];
    private readonly maxConcurrency: number;

    constructor(environments: string[], maxConcurrency: number = 25) {
        if (!environments || !Array.isArray(environments) || environments.length === 0) {
            throw new Error('Valid environments array is required');
        }
        if (maxConcurrency <= 0) {
            throw new Error('Max concurrency must be greater than 0');
        }

        this.environments = [...environments];
        this.maxConcurrency = maxConcurrency;
    }

    public async initClusterForDriver(driver: DriverInterface, browser: Browser): Promise<any> {
        if (!driver || !driver.name) {
            throw new Error('Valid driver with name is required');
        }
        if (!browser || !browser.product) {
            throw new Error('Valid browser with product is required');
        }

        const clusterKey = `${driver.name}-${browser.product}`;
        let clusterInfo = this.clusters.find(c => c.key === clusterKey);

        if (!clusterInfo) {
            try {
                const cluster = await driver.initCluster(this.maxConcurrency, browser);
                clusterInfo = {
                    cluster,
                    key: clusterKey,
                    driver,
                    browser,
                };
                this.clusters.push(clusterInfo);
            } catch (error) {
                throw new Error(`Failed to initialize cluster for ${clusterKey}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        }

        return clusterInfo.cluster;
    }

    async runTestCases(testCases: any[], screenshotProgress: any) {
        const screenshotPromises = [];

        for (const testCase of testCases) {
            for (const environment of this.environments) {
                const screenshotPromise = new Promise<void>((resolve, reject) => {
                    testCase.cluster.queue({
                        brand: testCase.brandName,
                        url: testCase.urlSet[environment],
                        viewport: testCase.viewport,
                        user: testCase.user,
                        test: environment,
                        filePath: testCase.filePath,
                        driver: testCase.driver,
                        testCase: testCase,
                        callback: (response: any) => {
                            try {
                                testCase.onScreenshotResult(environment, response);
                                screenshotProgress.increment();
                                resolve();
                            } catch (err) {
                                reject(err);
                            }
                        },
                    } as ScreenshotPageData, Screenshot.take);
                });

                screenshotPromises.push(screenshotPromise);
            }
        }

        await Promise.all(screenshotPromises);
    }

    async cleanupClusters() {
        await Promise.all(this.clusters.map(cluster => cluster.idle().then(() => cluster.close())));
    }

    getClusters() {
        return this.clusters;
    }
}

export default ClusterManager;
