import Screenshot from './Screenshot.ts';
import {ScreenshotPageData} from '@/shared/types/Common/ScreenshotPageData.ts';
import {DriverInterface} from './Driver/DriverInterface.ts';
import type Browser from '@/shared/types/Config/Browser.ts';
import type TestCase from './TestCase.ts';

interface ClusterInfo {
    cluster: any;
    key: string;
    driver: DriverInterface;
    browser: Browser;
}

interface ProgressTracker {
    increment(): void;
}

class ClusterManager {
    private readonly clusters: ClusterInfo[] = [];
    private readonly environments: readonly string[];
    private readonly maxConcurrency: number;

    constructor(environments: string[], maxConcurrency: number = 25) {
        if (!environments || !Array.isArray(environments) || environments.length === 0) {
            throw new Error('Valid environments array is required');
        }
        if (maxConcurrency <= 0) {
            throw new Error('Max concurrency must be greater than 0');
        }

        this.environments = [...environments];
        this.maxConcurrency = maxConcurrency;
    }

    public async initClusterForDriver(driver: DriverInterface, browser: Browser): Promise<any> {
        if (!driver || !driver.name) {
            throw new Error('Valid driver with name is required');
        }
        if (!browser || !browser.product) {
            throw new Error('Valid browser with product is required');
        }

        const clusterKey = `${driver.name}-${browser.product}`;
        let clusterInfo = this.clusters.find(c => c.key === clusterKey);

        if (!clusterInfo) {
            try {
                const cluster = await driver.initCluster(this.maxConcurrency, browser);
                clusterInfo = {
                    cluster,
                    key: clusterKey,
                    driver,
                    browser,
                };
                this.clusters.push(clusterInfo);
            } catch (error) {
                throw new Error(`Failed to initialize cluster for ${clusterKey}: ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        }

        return clusterInfo.cluster;
    }

    public async runTestCases(testCases: TestCase[], screenshotProgress: ProgressTracker): Promise<void> {
        if (!testCases || !Array.isArray(testCases)) {
            throw new Error('Valid test cases array is required');
        }
        if (!screenshotProgress || typeof screenshotProgress.increment !== 'function') {
            throw new Error('Valid progress tracker with increment method is required');
        }

        const screenshotPromises: Promise<void>[] = [];

        try {
            for (const testCase of testCases) {
                for (const environment of this.environments) {
                    const screenshotPromise = this.createScreenshotPromise(testCase, environment, screenshotProgress);
                    screenshotPromises.push(screenshotPromise);
                }
            }

            await Promise.all(screenshotPromises);
        } catch (error) {
            throw new Error(`Failed to run test cases: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    private createScreenshotPromise(
        testCase: TestCase,
        environment: string,
        screenshotProgress: ProgressTracker
    ): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            try {
                const screenshotData: ScreenshotPageData = {
                    url: testCase.urlSet[environment as keyof typeof testCase.urlSet],
                    viewport: testCase.viewport,
                    user: testCase.user,
                    test: environment,
                    filePath: testCase.filePath,
                    driver: testCase.driver,
                    testCase: testCase,
                    callback: (response: any) => {
                        try {
                            testCase.onScreenshotResult(environment, response);
                            screenshotProgress.increment();
                            resolve();
                        } catch (err) {
                            reject(new Error(`Screenshot callback failed: ${err instanceof Error ? err.message : 'Unknown error'}`));
                        }
                    },
                };

                testCase.cluster.queue(screenshotData, Screenshot.take);
            } catch (error) {
                reject(new Error(`Failed to queue screenshot: ${error instanceof Error ? error.message : 'Unknown error'}`));
            }
        });
    }

    public async cleanupClusters(): Promise<void> {
        try {
            const cleanupPromises = this.clusters.map(async (clusterInfo) => {
                try {
                    await clusterInfo.cluster.idle();
                    await clusterInfo.cluster.close();
                } catch (error) {
                    console.error(`Failed to cleanup cluster ${clusterInfo.key}:`, error);
                }
            });

            await Promise.all(cleanupPromises);
            this.clusters.length = 0; // Clear the clusters array
        } catch (error) {
            throw new Error(`Failed to cleanup clusters: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    public getClusters(): readonly ClusterInfo[] {
        return [...this.clusters];
    }

    public getEnvironments(): readonly string[] {
        return [...this.environments];
    }
}

export default ClusterManager;
