import Screenshot from './Screenshot.ts';
import {ScreenshotPageData} from '@/shared/types/Common/ScreenshotPageData.ts';

class ClusterManager {
    private clusters: any[] = [];
    private readonly environments: string[];

    constructor(environments: string[]) {
        this.environments = environments;
    }

    async initClusterForDriver(driver: any, browser: any) {
        const clusterKey = `${driver.name}-${browser.product}`;

        let cluster = this.clusters.find(c => c.key === clusterKey);

        if (!cluster) {
            cluster = await driver.initCluster(25, browser);
            cluster.key = clusterKey;
            this.clusters.push(cluster);
        }

        return cluster;
    }

    async runTestCases(testCases: any[], screenshotProgress: any) {
        const screenshotPromises = [];

        for (const testCase of testCases) {
            for (const environment of this.environments) {
                const screenshotPromise = new Promise<void>((resolve, reject) => {
                    testCase.cluster.queue({
                        brand: testCase.brandName,
                        url: testCase.urlSet[environment],
                        viewport: testCase.viewport,
                        user: testCase.user,
                        test: environment,
                        filePath: testCase.filePath,
                        driver: testCase.driver,
                        testCase: testCase,
                        callback: (response: any) => {
                            try {
                                testCase.onScreenshotResult(environment, response);
                                screenshotProgress.increment();
                                resolve();
                            } catch (err) {
                                reject(err);
                            }
                        },
                    } as ScreenshotPageData, Screenshot.take);
                });

                screenshotPromises.push(screenshotPromise);
            }
        }

        await Promise.all(screenshotPromises);
    }

    async cleanupClusters() {
        await Promise.all(this.clusters.map(cluster => cluster.idle().then(() => cluster.close())));
    }

    getClusters() {
        return this.clusters;
    }
}

export default ClusterManager;
