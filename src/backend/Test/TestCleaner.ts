import {cloneDeep} from 'lodash-es';
import * as fs from 'node:fs';
import Log from '../Common/Log.ts';
import FileWriter from './FileWriter.ts';
import Application from '@/backend/Lib/Application.ts';

class TestCleaner {
    private static readonly retainDirectoryCount = 5;

    static cleanup(previouslyRanTestsArray: string[]) {
        const screenshotPath = Application.screenshotPath;
        const directoriesToKeep = TestCleaner.retainDirectoryCount;
        const directoriesToDelete = [];
        cloneDeep(previouslyRanTestsArray)
            .reverse()
            .forEach((directory, index) => {
                const absoluteDirectory = `${screenshotPath}/${directory}`;

                if (index >= directoriesToKeep) {
                    directoriesToDelete.push(absoluteDirectory);
                }
            });

        Log.info('Removing old test runs...');

        directoriesToDelete.map(absoluteDirectory => {
            fs.rmSync(absoluteDirectory, {
                recursive: true,
                force: true,
            });

            return absoluteDirectory.split('/').pop();
        });

        Log.info(`[Debug] ${directoriesToDelete.length} old tests were cleaned up`);

        FileWriter.writeSafe(
            `${screenshotPath}/_datestamps.json`,
            JSON.stringify(
                fs.readdirSync(screenshotPath, {withFileTypes: true})
                    .filter(directory => directory.isDirectory())
                    .map(directory => directory.name),
                undefined,
                2,
            ),
            false,
        );
    }
}

export default TestCleaner;
