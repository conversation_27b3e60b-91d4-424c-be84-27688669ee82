import urlQueryParameters from '@/shared/config/urlQueryParameters.ts';
import {forEach} from 'lodash-es';

class UrlSet {
    public domain: string;
    public url: string;
    public local: string;
    public production: string;

    constructor(domain: string, url: string) {
        let query = this.buildQuery();

        this.domain = domain;
        this.url = url;
        this.local = this.generateUrl('.user.ldev.nl', query);
        this.production = this.generateUrl('', query);
    }

    private buildQuery(): string {
        let query = '';
        const queryParts = [];

        forEach(urlQueryParameters, (value, key) => {
            queryParts.push(`${key}=${value}`);
        });

        query = `?${queryParts.join('&')}`;

        return query;
    }

    private generateUrl(appendEnvDomain: string, query: string): string {
        const fullUrl = `${this.domain}${appendEnvDomain}/${this.url}`.replace('//', '/');

        return `https://${fullUrl}${query}`;
    }
}

export default UrlSet;
