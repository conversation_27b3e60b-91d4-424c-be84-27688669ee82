import {DriverInterface} from './DriverInterface.ts';
import Browser from '@/shared/types/Config/Browser.ts';
import {Cluster} from 'playwright-cluster';

export class PlaywrightDriver implements DriverInterface {
    name = 'playwright';

    async initCluster(maxConcurrency: number, browser: Browser): Promise<Cluster> {
        return Cluster.launch({
            concurrency: Cluster.CONCURRENCY_PAGE,
            maxConcurrency: maxConcurrency,
            retryLimit: 2,
            timeout: 60000,

            playwrightOptions: {
                headless: true,
                args: ['--no-sandbox', '--disable-setuid-sandbox'],
                executablePath: browser.executable,
                ignoreDefaultArgs: ['--hide-scrollbars'],
            },
        });
    }

    async preparePage(page, data) {
        await page.setViewportSize(data.viewport);

        return page;
    }
}
