import {DriverInterface} from './DriverInterface.ts';
import {PuppeteerDriver} from './PuppeteerDriver.ts';
import {PlaywrightDriver} from './PlaywrightDriver.ts';

export class DriverFactory {
    static createDriver(driverType: string): DriverInterface {
        switch (driverType.toLowerCase()) {
            case 'puppeteer':
                return new PuppeteerDriver();
            case 'playwright':
                return new PlaywrightDriver();
            default:
                throw new Error(`Unknown driver type: ${driverType}`);
        }
    }
}
