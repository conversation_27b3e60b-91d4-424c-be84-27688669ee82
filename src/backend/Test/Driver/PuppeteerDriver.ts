import {Cluster} from 'puppeteer-cluster';
import {Product} from 'puppeteer';
import Browser from '@/shared/types/Config/Browser.ts';
import {DriverInterface} from './DriverInterface.ts';

export class PuppeteerDriver implements DriverInterface {
    name = 'puppeteer';

    async initCluster(maxConcurrency: number, browser: Browser): Promise<Cluster> {
        return Cluster.launch({
            concurrency: Cluster.CONCURRENCY_PAGE,
            maxConcurrency: maxConcurrency,
            retryLimit: 2,
            timeout: 60000,

            puppeteerOptions: {
                headless: 'new',
                args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-features=site-per-process'],
                executablePath: browser.executable,
                product: browser.product as Product,
                ignoreDefaultArgs: ['--hide-scrollbars'],
            },
        });
    }

    async preparePage(page, data) {
        await page.setViewport(data.viewport);

        return page;
    }
}
