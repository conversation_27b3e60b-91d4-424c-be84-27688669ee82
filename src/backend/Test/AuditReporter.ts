import Log from '../Common/Log.ts';
import chalk from 'ansis';
import ScreenshotCompare from './ScreenshotCompare.ts';
import TestCase from './TestCase.ts';
import FileWriter from './FileWriter.ts';
import {SingleBar} from 'cli-progress';
import ProgressBar from './ProgressBar.ts';
import ScreenshotTestResult from './ScreenshotTestResult.ts';
import Application from '@/backend/Lib/Application.ts';

class AuditReporter {
    static async report(testCases: TestCase[], date: string) {
        Log.info(chalk.blue('\nDiffing images and creating report...'));
        const diffProgress = ProgressBar.create();
        diffProgress.start(testCases.length, 0);

        const audit = await AuditReporter.performAudit(testCases, diffProgress, date);

        diffProgress.stop();
        Log.info(
            `\n\n⚠️  A total of ${audit.length} page(s) had non-acceptable differences.`,
        );
        audit.forEach((screenshotTestResult: ScreenshotTestResult) => {
            Log.info(chalk.red(
                `- ${screenshotTestResult.testCase.brandSlug} | Path: ${screenshotTestResult.testCase.urlSet.url} | Viewport: ${screenshotTestResult.testCase.viewport.name} | Driver: ${screenshotTestResult.testCase.driver.name}`,
            ));
        });
    }

    static async performAudit(testCases: TestCase[], diffProgress: SingleBar, date: string) {
        const screenshotPath = Application.screenshotPath;
        const audit = [];
        await Promise.all(testCases.map(async testCase => {
            const result = await ScreenshotCompare.testCase(testCase);
            if (!result.passing) {
                audit.push(result.toAuditData());
            }
            diffProgress.increment();
        }));

        FileWriter.writeSafe(`${screenshotPath}/${date}/audit.json`, JSON.stringify(audit), false);
        return audit;
    }
}

export default AuditReporter;
