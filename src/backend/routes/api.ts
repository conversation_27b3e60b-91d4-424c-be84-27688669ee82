import express from 'express';
import TestRunController from '../Controllers/TestRunController.ts';

const router = express.Router();

// Test execution routes
router.post('/run-test', TestRunController.runTest);
router.get('/test-status', TestRunController.getStatus);

// Health check route
router.get('/health', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Oracle API is running',
        timestamp: new Date().toISOString(),
    });
});

export default router;
